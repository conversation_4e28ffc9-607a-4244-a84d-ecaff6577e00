import { Context, Service } from 'koishi';
import { Member, Turn, Event } from '../services/worldstate';


// --- 输出接口定义 ---

export interface FlowAnalysis {
    pace: 'fast' | 'normal' | 'slow' | 'stale';
    intensity: number; // 0-1
    turnTaking: 'monologue' | 'dialogue' | 'chaotic';
    participants: Member[];
    isTopicStarter: boolean;
    silenceSinceLastTurnMs: number;
    // 未来可以增加更多分析维度，如情感分析等
}


export class ConversationFlowAnalyzerService extends Service {
    constructor(ctx: Context) {
        super(ctx, 'agent.analyzer.flow', true);
    }

    /**
     * 分析一个回合的对话流。
     * @param turn 要分析的当前回合
     * @param lastTurn 上一个回合，用于计算沉默时间
     * @returns 结构化的对话流分析结果
     */
    public analyze(turn: Turn, lastTurn?: Turn): FlowAnalysis {
        const events = turn.events;
        if (events.length === 0) {
            // 如果回合没有事件，返回一个默认的静默状态
            return this.createStaleFlowAnalysis(turn, lastTurn);
        }

        const participants = this.getParticipants(events);
        const pace = this.calculatePace(events);
        const turnTaking = this.determineTurnTaking(participants, events);
        const silenceSinceLastTurnMs = lastTurn ? turn.startTimestamp.getTime() - lastTurn.endTimestamp.getTime() : 0;

        return {
            pace,
            intensity: this.calculateIntensity(events),
            turnTaking,
            participants,
            isTopicStarter: !lastTurn || silenceSinceLastTurnMs > 60 * 1000, // 超过1分钟沉默算新话题
            silenceSinceLastTurnMs,
        };
    }

    private getParticipants(events: Event[]): Member[] {
        const participantMap = new Map<string, Member>();
        for (const event of events) {
            const actor = (event.payload as any).actor;
            if (actor) {
                participantMap.set(actor.id, actor);
            }
        }
        return Array.from(participantMap.values());
    }

    private calculatePace(events: Event[]): FlowAnalysis['pace'] {
        if (events.length < 2) return 'normal';

        const timestamps = events.map(e => e.timestamp.getTime());
        const intervals = [];
        for (let i = 1; i < timestamps.length; i++) {
            intervals.push(timestamps[i] - timestamps[i-1]);
        }

        const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;

        if (avgInterval < 5000) return 'fast'; // 平均间隔小于5秒
        if (avgInterval > 45000) return 'slow'; // 平均间隔大于45秒
        return 'normal';
    }

    private calculateIntensity(events: Event[]): number {
        // 简化版：基于消息数量和平均长度
        const messageEvents = events.filter(e => e.type === 'message');
        if (messageEvents.length === 0) return 0;

        const avgLength = messageEvents.reduce((sum, e) => sum + ((e.payload as any).content?.length || 0), 0) / messageEvents.length;

        // 将强度映射到 0-1 范围
        const intensity = Math.min(1, (messageEvents.length / 5) * (avgLength / 50));
        return parseFloat(intensity.toFixed(2));
    }

    private determineTurnTaking(participants: Member[], events: Event[]): FlowAnalysis['turnTaking'] {
        if (participants.length === 1) return 'monologue';
        if (participants.length === 2) {
            // 检查发言是否交替
            // (简化逻辑，实际需要更复杂的判断)
            return 'dialogue';
        }
        return 'chaotic';
    }

    private createStaleFlowAnalysis(turn: Turn, lastTurn?: Turn): FlowAnalysis {
        const silenceSinceLastTurnMs = lastTurn ? turn.startTimestamp.getTime() - lastTurn.endTimestamp.getTime() : 0;
        return {
            pace: 'stale',
            intensity: 0,
            turnTaking: 'monologue',
            participants: [],
            isTopicStarter: true,
            silenceSinceLastTurnMs,
        }
    }
}