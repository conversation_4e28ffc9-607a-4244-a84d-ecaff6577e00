import { Context, Service } from "koishi";
import { WorldStateService } from "../services";
import { ConversationFlowAnalyzerService, FlowAnalysis } from "./conversation-flow-analyzer";
import { PromptBuilder } from "./prompt-builder";
import { Willingness, WillingnessCalculator, WillingnessConfig } from "./willingness-calculator";

// --- 整体 Agent 服务的配置接口 ---
export interface AgentConfig {
    willingness: WillingnessConfig;
    // 未来可以加入 LLM, ToolExecutor 等配置
}

// 在 Koishi 上下文中声明 AgentService
declare module "koishi" {
    interface Context {
        agent: AgentService;
    }
}

export class AgentService extends Service<AgentConfig> {
    // --- 内部组件实例 ---
    private readonly analyzer: ConversationFlowAnalyzerService;
    private readonly calculator: WillingnessCalculator;
    private readonly promptBuilder: PromptBuilder;

    // --- 依赖的服务 ---
    private readonly worldState: WorldStateService;

    // --- 状态管理 ---
    private debounceTimers = new Map<string, NodeJS.Timeout>();
    private processingChannels = new Set<string>();
    private readonly AROUSAL_DEBOUNCE_DELAY = 1500; // 防抖延迟

    constructor(ctx: Context, config: AgentConfig) {
        super(ctx, "agent", true); // 将服务注册为 'agent'
        this.config = config;

        // --- 依赖注入与组件实例化 ---
        this.worldState = ctx["yesimbot.worldState"];
        if (!this.worldState) {
            throw new Error("WorldStateService is not available! Make sure it is loaded before AgentService.");
        }

        // 实例化内部组件，将配置和上下文传入
        this.analyzer = new ConversationFlowAnalyzerService(ctx);
        this.calculator = new WillingnessCalculator(config.willingness, ctx);
        this.promptBuilder = new PromptBuilder(ctx);
    }

    protected start(): void {
        this.ctx.logger("agent").info("AgentService started. Awaiting world state updates...");

        // 监听来自 WorldStateService 的“回合更新”事件
        this.ctx.on("worldstate/turn-updated", (turnId, channelId, platform) => {
            this.handleArousal(channelId, platform);
        });

        // (可选) 启动低频破冰检查
        // setInterval(() => this.checkForStaleChannels(), 10 * 60 * 1000);
    }

    /**
     * "唤醒"处理流程，由事件触发。
     * 负责防抖，确保短时间内的连续事件被合并处理。
     */
    private handleArousal(channelId: string, platform: string): void {
        const channelKey = `${platform}:${channelId}`;
        if (this.processingChannels.has(channelKey)) return;

        if (this.debounceTimers.has(channelKey)) {
            clearTimeout(this.debounceTimers.get(channelKey)!);
        }

        const timer = setTimeout(() => {
            this.debounceTimers.delete(channelKey);
            this.processChannel(channelId, platform).catch((err) =>
                this.ctx.logger("agent").error(`Error processing channel ${channelKey}:`, err)
            );
        }, this.AROUSAL_DEBOUNCE_DELAY);

        this.debounceTimers.set(channelKey, timer);
    }

    /**
     * 决策与处理的核心入口。
     * 在被唤醒后，评估状态、计算意愿，并决定是否启动完整的思考循环。
     */
    private async processChannel(channelId: string, platform: string): Promise<void> {
        const channelKey = `${platform}:${channelId}`;

        // --- 1. 获取最新状态 ---
        const latestTurn = await this.worldState.turns.getLatestTurnWithEvents(platform, channelId);
        if (!latestTurn || latestTurn.status !== "new") return; // 只处理未被处理的新回合

        const lastTurn = await this.worldState.turns.getSecondLatestTurn(platform, channelId);
        const channelInfo = await this.worldState.getChannelInfo(platform, channelId); // 需要 WorldStateService 提供一个轻量级获取频道信息的方法

        // --- 2. 状态分析与意愿计算 ---
        const flowAnalysis = this.analyzer.analyze(latestTurn, lastTurn);
        const willingness = this.calculator.calculate(flowAnalysis, latestTurn, channelInfo);

        this.ctx
            .logger("agent:decision")
            .info(`[${channelKey}] Willingness: ${willingness.toEngage.toFixed(2)} | Threshold: ${willingness.threshold.toFixed(2)}`);

        // --- 3. 决策：是否行动 ---
        if (willingness.toEngage < willingness.threshold) {
            this.ctx.logger("agent:decision").info(`[${channelKey}] Willingness too low. No action taken.`);
            return; // 意愿不足，Agent 保持沉默
        }

        this.ctx
            .logger("agent:decision")
            .info(`[${channelKey}] Willingness threshold met. Starting full processing for turn ${latestTurn.id}.`);
        this.processingChannels.add(channelKey);

        try {
            // --- 4. 启动完整的思考-行动循环 ---
            await this.executeThinkingCycle(latestTurn.id, { flowAnalysis, willingness });
        } finally {
            this.processingChannels.delete(channelKey);
        }
    }

    /**
     * 思考-行动循环 (原 AgentProcessorService 的职责)。
     */
    private async executeThinkingCycle(
        turnId: string,
        assessment: { flowAnalysis: FlowAnalysis; willingness: Willingness }
    ): Promise<void> {
        // a. 标记回合为处理中
        await this.worldState.updateTurnStatus(turnId, "in_progress");

        // b. 获取完整的世界状态快照
        const allowedChannels = [assessment.flowAnalysis.channelId]; // 简化：只看当前频道
        const worldState = await this.worldState.getWorldState(allowedChannels);
        const targetTurn = worldState.activeChannels[0]?.history.find((t) => t.id === turnId);
        if (!targetTurn) throw new Error(`Turn ${turnId} not found in world state.`);

        // c. 构建提示词
        const prompt = this.promptBuilder.build({
            worldState,
            willingness: assessment.willingness,
            flowAnalysis: assessment.flowAnalysis,
            targetTurn,
        });

        // d. 启动心跳循环 (Heartbeat Loop)
        let requestHeartbeat = true;
        let currentPrompt = prompt;

        while (requestHeartbeat) {
            this.ctx.logger("agent:cycle").info(`[${turnId}] Heartbeat loop running...`);

            // i. 调用 LLM
            // const llmResponseJson = await this.llm.call(currentPrompt);
            // const { thoughts, actions, request_heartbeat } = JSON.parse(llmResponseJson);
            // requestHeartbeat = request_heartbeat;

            // --- 临时模拟 ---
            const thoughts = { observe: "...", analyze_infer: "...", plan: "..." };
            const actions = [{ function: "sendMessage", params: { content: `Hello! I have decided to respond to turn ${turnId}.` } }];
            requestHeartbeat = false;
            // --- 结束模拟 ---

            // ii. 执行行动并获取观察结果
            // const observations = await this.toolExecutor.execute(actions);

            // iii. 持久化此心跳的完整记录
            // await this.worldState.storeAgentHeartbeat(turnId, { thoughts, actions, observations });

            // iv. 如果需要，为下一次心跳构建新提示词
            // if (requestHeartbeat) {
            //   currentPrompt = this.promptBuilder.buildForNextHeartbeat(...);
            // }
        }

        // e. 循环结束，标记回合完成
        await this.worldState.updateTurnStatus(turnId, "completed");
        this.ctx.logger("agent:cycle").info(`[${turnId}] Thinking cycle completed.`);
    }
}
