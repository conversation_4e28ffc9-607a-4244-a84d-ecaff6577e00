<task>
'packages/core/src/services/worldstate/repositories' (see below for file content)
'packages/core/src/services/worldstate' (see below for file content)
</task>

<file_content path="packages/core/src/services/worldstate/repositories">
├── member-repository.ts
└── turn-repository.ts

<file_content path="packages/core/src/services/worldstate/repositories/member-repository.ts">
 1 | import { Context } from "koishi";
 2 | import { Member } from "../interfaces";
 3 |
 4 | export class MemberRepository {
 5 |     constructor(private ctx: Context) {}
 6 |
 7 |     /**
 8 |      * 根据一组平台用户ID，高效地获取他们在一个频道中的完整 Member 对象
 9 |      */
10 |     async getFullMembersByPlatformIds(platform: string, channelId: string, platformIds: string[]): Promise<Member[]> {
11 |         if (!platformIds || platformIds.length === 0) {
12 |             return [];
13 |         }
14 |
15 |         // 从平台ID找到内部用户ID
16 |         const bindingRecords = await this.ctx.database.get("binding", { platform, pid: platformIds });
17 |         const userIds = bindingRecords.map((b) => b.bid);
18 |         if (userIds.length === 0) {
19 |             return [];
20 |         }
21 |
22 |         // 创建用户ID到平台ID的映射
23 |         const userIdToPlatformIdMap = new Map(bindingRecords.map((b) => [b.bid, b.pid]));
24 |
25 |         // 并行查询用户和成员表
26 |         const [userRecords, memberRecords] = await Promise.all([
27 |             this.ctx.database.get("user", { id: userIds }),
28 |             this.ctx.database.get("members", { userId: userIds, platform, channelId }),
29 |         ]);
30 |
31 |         // 创建快速查找映射
32 |         const userMap = new Map(userRecords.map((u) => [u.id, u]));
33 |         const memberMap = new Map(memberRecords.map((m) => [m.userId, m]));
34 |
35 |         const result: Member[] = [];
36 |
37 |         for (const userId of userIds) {
38 |             const userRecord = userMap.get(userId);
39 |             const memberRecord = memberMap.get(userId);
40 |             const platformId = userIdToPlatformIdMap.get(userId);
41 |
42 |             if (!userRecord || !platformId) {
43 |                 continue;
44 |             }
45 |
46 |             const nick = memberRecord?.nick ?? userRecord.name;
47 |             const role = memberRecord?.role;
48 |             const lastActive = memberRecord?.lastActive;
49 |
50 |             result.push({
51 |                 id: platformId,
52 |                 name: userRecord.name ?? "未知用户",
53 |                 created_at: userRecord.createdAt,
54 |                 updated_at: userRecord.updatedAt,
55 |                 channel_id: channelId,
56 |                 last_active: lastActive?.toISOString(),
57 |                 meta: {
58 |                     avatar: userRecord.avatar,
59 |                     nick: nick,
60 |                     role: role,
61 |                 },
62 |             });
63 |         }
64 |         return result;
65 |     }
66 |
67 |     /**
68 |      * 获取指定频道的所有成员的完整信息
69 |      */
70 |     async getFullMembers(platform: string, channelId: string): Promise<Member[]> {
71 |         // 获取该频道所有成员的记录
72 |         const memberRecords = await this.ctx.database.get("members", { platform, channelId });
73 |         const userIds = memberRecords.map((m) => m.userId);
74 |         if (userIds.length === 0) return [];
75 |
76 |         // 获取这些内部ID对应的平台ID
77 |         const bindingRecords = await this.ctx.database.get("binding", { bid: userIds, platform });
78 |         const platformIds = bindingRecords.map((b) => b.pid);
79 |
80 |         return this.getFullMembersByPlatformIds(platform, channelId, platformIds);
81 |     }
82 | }

</file_content>

<file_content path="packages/core/src/services/worldstate/repositories/turn-repository.ts">
  1 | import { Context } from "koishi";
  2 | import {
  3 |     Action,
  4 |     ActionResult,
  5 |     AgentResponse,
  6 |     ChannelEvent,
  7 |     MessageEvent,
  8 |     SystemNotificationEvent,
  9 |     Turn,
 10 |     UserJoinedEvent,
 11 |     UserLeftEvent,
 12 | } from "../interfaces";
 13 | import { AgentResponseData, TurnData } from "../model";
 14 | import { MemberRepository } from "./member-repository";
 15 |
 16 | export class TurnRepository {
 17 |     constructor(private ctx: Context, private memberRepo: MemberRepository) {}
 18 |
 19 |     async getFullTurns(platform: string, channelId: string): Promise<Turn[]> {
 20 |         const turnRecords = await this.ctx.database.get("turns", { platform, channelId });
 21 |         if (!turnRecords.length) return [];
 22 |         return Promise.all(turnRecords.map((turn) => this.buildFullTurn(turn, platform, channelId)));
 23 |     }
 24 |
 25 |     /**
 26 |      * 获取或创建当前回合
 27 |      */
 28 |     async getCurrentTurn(platform: string, channelId: string): Promise<TurnData> {
 29 |         // 查找最近的活跃回合
 30 |         const recentTurns = await this.ctx.database
 31 |             .select("turns")
 32 |             .where({ platform, channelId, status: "active" })
 33 |             .orderBy("startTimestamp", "desc")
 34 |             .limit(1)
 35 |             .execute();
 36 |
 37 |         if (recentTurns.length > 0) {
 38 |             return recentTurns[0];
 39 |         }
 40 |
 41 |         // 创建新回合
 42 |         const newTurn: Partial<TurnData> = {
 43 |             id: `turn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
 44 |             channelId,
 45 |             platform,
 46 |             status: "active",
 47 |             summary: "",
 48 |             startTimestamp: new Date(),
 49 |             endTimestamp: new Date(),
 50 |         };
 51 |
 52 |         await this.ctx.database.create("turns", newTurn);
 53 |         return newTurn as TurnData;
 54 |     }
 55 |
 56 |     private async buildFullTurn(turnRecord: TurnData, platform: string, channelId: string): Promise<Turn> {
 57 |         // 获取此回合下的所有事件记录和AI响应
 58 |         const [eventRecords, responseRecords] = await Promise.all([
 59 |             this.ctx.database.get("channel_events", { turnId: turnRecord.id }, { sort: { timestamp: "asc" } }),
 60 |             this.ctx.database.get("agent_responses", { turnId: turnRecord.id }),
 61 |         ]);
 62 |
 63 |         // 构建AI响应数组
 64 |         const responses = this.buildAgentResponses(responseRecords);
 65 |
 66 |         // 获取所有事件中涉及到的成员信息
 67 |         const allMemberPlatformIds = new Set<string>();
 68 |         for (const event of eventRecords) {
 69 |             const data = event.data as any;
 70 |             if (data.actorId) allMemberPlatformIds.add(data.actorId);
 71 |             if (data.userId) allMemberPlatformIds.add(data.userId);
 72 |             if (data.senderId) allMemberPlatformIds.add(data.senderId);
 73 |         }
 74 |
 75 |         // 获取完整的成员对象并建立快速查找映射
 76 |         const memberMap = new Map(
 77 |             (await this.memberRepo.getFullMembersByPlatformIds(platform, channelId, Array.from(allMemberPlatformIds))).map((m) => [m.id, m])
 78 |         );
 79 |
 80 |         // 构建事件数组
 81 |         const events: ChannelEvent[] = eventRecords
 82 |             .map((record) => {
 83 |                 const data = record.data as any;
 84 |                 const base = { id: record.id, timestamp: record.timestamp };
 85 |                 const systemActor = { id: "system", name: "System", meta: {} };
 86 |
 87 |                 switch (record.type) {
 88 |                     case "user_joined":
 89 |                         return {
 90 |                             ...base,
 91 |                             type: "user_joined",
 92 |                             actor: memberMap.get(data.actorId) ?? systemActor,
 93 |                             user: memberMap.get(data.userId) ?? { id: data.userId, name: "未知用户" },
 94 |                             note: data.note,
 95 |                         } as UserJoinedEvent;
 96 |                     case "user_left":
 97 |                         return {
 98 |                             ...base,
 99 |                             type: "user_left",
100 |                             actor: memberMap.get(data.actorId) ?? systemActor,
101 |                             user: memberMap.get(data.userId) ?? { id: data.userId, name: "未知用户" },
102 |                             reason: data.reason,
103 |                         } as UserLeftEvent;
104 |                     case "message":
105 |                     case "message_sent":
106 |                         return {
107 |                             ...base,
108 |                             type: "message",
109 |                             messageId: data.messageId,
110 |                             sender: memberMap.get(data.senderId) ?? { id: data.senderId, name: "未知用户" },
111 |                             content: data.content,
112 |                         } as MessageEvent;
113 |                     case "system_notification":
114 |                         return { ...base, type: "system_notification", content: data.content } as SystemNotificationEvent;
115 |                     default:
116 |                         return null;
117 |                 }
118 |             })
119 |             .filter(Boolean)
120 |             .map((event) => {
121 |                 // 添加布尔标记用于模板渲染
122 |                 const templateEvent: any = { ...event };
123 |                 templateEvent[`is_${event.type}`] = true;
124 |                 return templateEvent;
125 |             });
126 |
127 |         return {
128 |             id: turnRecord.id,
129 |             status: turnRecord.status,
130 |             summary: turnRecord.summary,
131 |             responses,
132 |             events,
133 |         };
134 |     }
135 |
136 |     /**
137 |      * 将数据库中的 AgentResponseData 记录转换为业务层的 AgentResponse 对象数组
138 |      */
139 |     private buildAgentResponses(records: AgentResponseData[]): AgentResponse[] {
140 |         if (!records || records.length === 0) return [];
141 |         return records.map((record) => ({
142 |             thoughts: this.validateThoughts(record.thoughts),
143 |             actions: this.validateActions(record.actions),
144 |             observations: this.validateObservations(record.observations),
145 |         }));
146 |     }
147 |
148 |     private validateThoughts(data: any): AgentResponse["thoughts"] {
149 |         if (typeof data !== "object" || data === null) return { obverse: "", analyze_infer: "", plan: "" };
150 |         return {
151 |             obverse: typeof data.obverse === "string" ? data.obverse : "",
152 |             analyze_infer: typeof data.analyze_infer === "string" ? data.analyze_infer : "",
153 |             plan: typeof data.plan === "string" ? data.plan : "",
154 |         };
155 |     }
156 |
157 |     private validateActions(data: any): Action[] {
158 |         if (!Array.isArray(data)) return [];
159 |         return data
160 |             .filter((item) => typeof item === "object" && item !== null && typeof item.function === "string")
161 |             .map((item) => ({
162 |                 function: item.function,
163 |                 params: typeof item.params === "object" && item.params !== null ? item.params : {},
164 |                 renderParams() {
165 |                     return JSON.stringify(this.params);
166 |                 },
167 |             }));
168 |     }
169 |
170 |     private validateObservations(data: any): ActionResult[] {
171 |         if (!Array.isArray(data)) return [];
172 |         const observations = data.filter((item) => typeof item === "object" && item !== null) as ActionResult[];
173 |         observations.forEach((item) => {
174 |             item.renderResult = function () {
175 |                 const result = this.result?.result || this.result?.error || "";
176 |                 return typeof result === "string" ? result : JSON.stringify(result);
177 |             };
178 |         });
179 |         return observations;
180 |     }
181 | }

</file_content>
</file_content>

<file_content path="packages/core/src/services/worldstate">
├── config.ts
├── index.ts
├── interfaces.ts
├── model.ts
├── README.md
├── repositories/
├── simple-message-classifier.ts
└── world-state-service.ts

<file_content path="packages/core/src/services/worldstate/config.ts">
 1 | import { Schema } from "koishi";
 2 |
 3 | export interface WorldStateConfig {
 4 |     /**
 5 |      * 成员信息更新间隔（分钟）
 6 |      */
 7 |     MemberUpdateInterval: number;
 8 |
 9 |     /**
10 |      * 频道信息更新间隔（分钟）
11 |      */
12 |     ChannelUpdateInterval: number;
13 |
14 |     /**
15 |      * 活跃回合保留时间（小时）
16 |      */
17 |     ActiveTurnRetention: number;
18 |
19 |     /**
20 |      * 最大活跃回合数
21 |      */
22 |     MaxActiveTurns: number;
23 |
24 |     /**
25 |      * 是否自动折叠过期回合
26 |      */
27 |     AutoFoldExpiredTurns: boolean;
28 |
29 |     /**
30 |      * 最小消息长度
31 |      */
32 |     MinMessageLength: number;
33 |
34 |     /**
35 |      * 数据保留期限（天）
36 |      */
37 |     DataRetentionDays: number;
38 |
39 |     /**
40 |      * 活跃频道显示限制
41 |      */
42 |     ActiveChannelLimit: number;
43 |
44 |     /**
45 |      * 每个频道显示的最大回合数
46 |      */
47 |     MaxTurnsPerChannel: number;
48 |
49 |     /**
50 |      * 每个回合显示的最大事件数
51 |      */
52 |     MaxEventsPerTurn: number;
53 | }
54 |
55 | export const WorldStateConfigSchema: Schema<WorldStateConfig> = Schema.object({
56 |     MemberUpdateInterval: Schema.number().min(5).max(1440).default(60).description("成员信息更新间隔（分钟）"),
57 |     ChannelUpdateInterval: Schema.number().min(10).max(1440).default(120).description("频道信息更新间隔（分钟）"),
58 |     ActiveTurnRetention: Schema.number().min(1).max(168).default(24).description("活跃回合保留时间（小时）"),
59 |     MaxActiveTurns: Schema.number().min(5).max(100).default(20).description("最大活跃回合数"),
60 |     AutoFoldExpiredTurns: Schema.boolean().default(true).description("是否自动折叠过期回合"),
61 |     MinMessageLength: Schema.number().min(0).max(100).default(1).description("最小消息长度"),
62 |     DataRetentionDays: Schema.number().min(1).max(365).default(30).description("数据保留期限（天）"),
63 |     ActiveChannelLimit: Schema.number().min(1).max(50).default(10).description("活跃频道显示限制"),
64 |     MaxTurnsPerChannel: Schema.number().min(1).max(100).default(10).description("每个频道显示的最大回合数"),
65 |     MaxEventsPerTurn: Schema.number().min(5).max(500).default(50).description("每个回合显示的最大事件数"),
66 | });

</file_content>

<file_content path="packages/core/src/services/worldstate/index.ts">
1 | export * from "./config";
2 | export * from "./interfaces";
3 | export * from "./model";
4 | export * from "./repositories/member-repository";
5 | export * from "./repositories/turn-repository";
6 | export * from "./simple-message-classifier";
7 | export * from "./world-state-service";
8 |

</file_content>

<file_content path="packages/core/src/services/worldstate/interfaces.ts">
  1 | import { ToolCallResult } from "../extensions";
  2 |
  3 | export interface WorldState {
  4 |     timestamp: string;
  5 |     activeChannels: Channel[];
  6 |     inactiveChannels: Channel[];
  7 | }
  8 |
  9 | // 一个频道对象，替换原来的 Scenario
 10 | // 群组或是私聊，或者沙盒测试环境
 11 | export interface Channel {
 12 |     id: string; // 频道 ID
 13 |     name: string; // 频道名称，群聊就是群组名，私聊为“你和 <用户名> 的私聊”
 14 |     type: "guild" | "private" | "sandbox";
 15 |     platform: string;
 16 |     meta: {
 17 |         description?: string; // 频道描述，有些适配器获取不到。或许可以根据历史对话生成一个
 18 |     };
 19 |     // 经过智能筛选和摘要的成员信息
 20 |     // 层次1: 核心成员，如群主、管理员，或与自己有特殊关系的成员
 21 |     // 层次2: 上下文相关成员 (近期发言或被@)
 22 |     members: Member[];
 23 |
 24 |     // 层次3: 群体氛围感知的摘要信息
 25 |     memberSummary: MemberSummary;
 26 |     history: Turn[];
 27 | }
 28 |
 29 | export interface MemberSummary {
 30 |     total_count: number; // 频道成员总数
 31 |     online_count: number; // 频道在线成员数
 32 |     recent_active_members_count: number; // 频道近期活跃成员数
 33 | }
 34 |
 35 | export interface User {
 36 |     id: string; // 特点平台用户 ID (pid)
 37 |     name: string; // 用户名称
 38 |     meta: {
 39 |         avatar?: string; // 用户头像 URL
 40 |         [key: string]: unknown;
 41 |     };
 42 |     created_at: Date;
 43 |     updated_at: Date;
 44 | }
 45 |
 46 | export interface Member extends User {
 47 |     channel_id: string;
 48 |     meta: User["meta"] & {
 49 |         nick?: string;
 50 |         role?: string;
 51 |     };
 52 |     last_active?: string; // 用户上次活跃时间
 53 | }
 54 |
 55 | export interface Turn {
 56 |     id: string;
 57 |     status: "full" | "summarized" | "folded" | "new";
 58 |     events: ChannelEvent[];
 59 |     summary?: string; // 摘要
 60 |     responses: AgentResponse[];
 61 | }
 62 |
 63 | export interface AgentResponse {
 64 |     thoughts: Thought;
 65 |     actions: Action[];
 66 |     observations: ActionResult[];
 67 | }
 68 |
 69 | export interface Thought {
 70 |     obverse: string;
 71 |     analyze_infer: string;
 72 |     plan: string;
 73 | }
 74 |
 75 | export interface Action {
 76 |     function: string;
 77 |     params: Record<string, unknown>;
 78 |     renderParams?: () => string;
 79 | }
 80 |
 81 | export interface ActionResult {
 82 |     function: string;
 83 |     result: ToolCallResult;
 84 |     renderResult?: () => string;
 85 | }
 86 |
 87 | // --- 事件相关接口 ---
 88 |
 89 | // 基础事件结构
 90 | interface BaseEvent {
 91 |     id: number; // 自增 ID
 92 |     type: string;
 93 |     timestamp: Date;
 94 | }
 95 |
 96 | // 具体事件类型定义
 97 | export interface UserJoinedEvent extends BaseEvent {
 98 |     type: "user_joined";
 99 |     actor: Member; // 操作者 (可能是系统或其他成员)
100 |     user: Member; // 加入的成员
101 |     note?: string;
102 | }
103 |
104 | export interface UserLeftEvent extends BaseEvent {
105 |     type: "user_left";
106 |     actor: Member;
107 |     user: Member;
108 |     reason?: string;
109 | }
110 |
111 | export interface MessageEvent extends BaseEvent {
112 |     type: "message";
113 |     messageId: string;
114 |     sender: Member;
115 |     content: string;
116 | }
117 |
118 | export interface SystemNotificationEvent extends BaseEvent {
119 |     type: "system_notification";
120 |     content: string;
121 | }
122 |
123 | export type ChannelEvent = UserJoinedEvent | UserLeftEvent | MessageEvent | SystemNotificationEvent;

</file_content>

<file_content path="packages/core/src/services/worldstate/model.ts">
  1 | import { Context } from "koishi";
  2 | import { Action, ActionResult, Thought, Turn } from "./interfaces";
  3 |
  4 | // --- Data Transfer Objects (DTOs) / Database Table Schemas ---
  5 | // 这些接口精确匹配数据库中的一行数据
  6 |
  7 | export interface MemberData {
  8 |     userId: number;
  9 |     platform: string;
 10 |     channelId: string;
 11 |     nick: string;
 12 |     role: string;
 13 |     lastActive: Date;
 14 | }
 15 |
 16 | export interface TurnData {
 17 |     id: string;
 18 |     channelId: string;
 19 |     platform: string;
 20 |     status: Turn["status"];
 21 |     summary: string;
 22 |     startTimestamp: Date;
 23 |     endTimestamp: Date;
 24 | }
 25 |
 26 | export interface AgentResponseData {
 27 |     id: number;
 28 |     turnId: string;
 29 |     thoughts: Thought;
 30 |     actions: Action[];
 31 |     observations: ActionResult[];
 32 | }
 33 |
 34 | export interface ChannelEventData {
 35 |     id: number; // 自增主键，用于唯一标识和排序
 36 |     turnId: string; // 外键，关联到 Turn
 37 |     type: string; // 事件类型，如 'user_joined', 'message_sent'
 38 |     timestamp: Date; // 事件发生时间
 39 |     data: object; // JSON 字段，存储该事件类型的特定数据
 40 | }
 41 |
 42 | // --- Koishi-specific Table Augmentation ---
 43 | // 扩展 Koishi 的核心接口和表定义
 44 |
 45 | declare module "koishi" {
 46 |     interface User {
 47 |         avatar?: string;
 48 |         createdAt: Date;
 49 |         updatedAt: Date;
 50 |     }
 51 |
 52 |     interface Channel {
 53 |         name?: string;
 54 |         type?: "guild" | "private" | "sandbox";
 55 |         description?: string;
 56 |         totalMemberCount?: number;
 57 |         recentActiveCount?: number;
 58 |         lastActivityAt?: Date;
 59 |     }
 60 |
 61 |     interface Tables {
 62 |         members: MemberData;
 63 |         turns: TurnData;
 64 |         channel_events: ChannelEventData;
 65 |         agent_responses: AgentResponseData;
 66 |     }
 67 | }
 68 |
 69 | export const name = "yesimbot-models";
 70 | export const inject = ["database"];
 71 |
 72 | export function apply(ctx: Context) {
 73 |     ctx.model.extend("user", {
 74 |         avatar: "string",
 75 |         createdAt: "timestamp",
 76 |         updatedAt: "timestamp",
 77 |     });
 78 |
 79 |     ctx.model.extend("channel", {
 80 |         name: "string",
 81 |         type: "string",
 82 |         description: "text",
 83 |         totalMemberCount: "unsigned",
 84 |         recentActiveCount: "unsigned",
 85 |         lastActivityAt: "timestamp",
 86 |     });
 87 |
 88 |     ctx.model.extend(
 89 |         "members",
 90 |         {
 91 |             userId: "unsigned",
 92 |             platform: "string(255)",
 93 |             channelId: "string(255)",
 94 |             nick: "string",
 95 |             role: "string",
 96 |             lastActive: "timestamp",
 97 |         },
 98 |         {
 99 |             primary: ["userId", "platform", "channelId"],
100 |             foreign: {
101 |                 userId: ["user", "id"],
102 |                 channelId: ["channel", "id"],
103 |                 platform: ["channel", "platform"],
104 |             },
105 |         }
106 |     );
107 |
108 |     ctx.model.extend(
109 |         "channel_events",
110 |         {
111 |             id: "unsigned",
112 |             turnId: "string(64)",
113 |             type: "string(64)",
114 |             timestamp: "timestamp",
115 |             data: "json",
116 |         },
117 |         {
118 |             autoInc: true,
119 |             primary: "id",
120 |             foreign: {
121 |                 turnId: ["turns", "id"],
122 |             },
123 |         }
124 |     );
125 |
126 |     ctx.model.extend(
127 |         "turns",
128 |         {
129 |             id: "char(64)",
130 |             channelId: "char(64)",
131 |             platform: "char(64)",
132 |             status: "string",
133 |             summary: "text",
134 |             startTimestamp: "timestamp",
135 |             endTimestamp: "timestamp",
136 |         },
137 |         {
138 |             primary: "id",
139 |             foreign: {
140 |                 channelId: ["channel", "id"],
141 |                 platform: ["channel", "platform"],
142 |             },
143 |         }
144 |     );
145 |
146 |     ctx.model.extend(
147 |         "agent_responses",
148 |         {
149 |             id: "unsigned",
150 |             turnId: "char(64)",
151 |             thoughts: "json",
152 |             actions: "json",
153 |             observations: "json",
154 |         },
155 |         {
156 |             autoInc: true,
157 |             primary: "id",
158 |             foreign: {
159 |                 turnId: ["turns", "id"],
160 |             },
161 |         }
162 |     );
163 | }

</file_content>

<file_content path="packages/core/src/services/worldstate/README.md">
  1 | # WorldState 模块
  2 |
  3 | ## 概述
  4 |
  5 | WorldState 模块是为 LLM chatbot 设计的核心组件，负责收集、管理和组织群组消息、聊天记录、系统事件，并为 PromptBuilder 提供结构化数据用于渲染提示词。
  6 |
  7 | ## 简化后的模块结构
  8 |
  9 | ```
 10 | worldstate/
 11 | ├── config.ts                      # 简化的配置架构
 12 | ├── world-state-service.ts         # 核心服务（整合所有功能）
 13 | ├── interfaces.ts                  # 接口定义
 14 | ├── model.ts                      # 数据模型和数据库表定义
 15 | ├── simple-message-classifier.ts  # 简化的消息分类器
 16 | ├── repositories/                 # 数据访问层
 17 | │   ├── member-repository.ts
 18 | │   └── turn-repository.ts
 19 | └── README.md                     # 本文档
 20 | ```
 21 |
 22 | ## 优化后的功能特点
 23 |
 24 | ### 1. 简化的配置架构 ✅
 25 |
 26 | **实现位置**: `config.ts`
 27 |
 28 | - **扁平化配置**: 移除过深的嵌套层级
 29 | - **PascalCase 命名**: 统一使用 PascalCase 配置属性命名
 30 | - **精简选项**: 只保留核心必要的配置项
 31 | - **依赖 Koishi**: 充分利用 Koishi 框架的内置配置管理
 32 |
 33 | ### 2. 统一的核心服务 ✅
 34 |
 35 | **实现位置**: `world-state-service.ts`
 36 |
 37 | - **单一职责**: 专注于 WorldState 的核心功能
 38 | - **集成管理**: 将分散的功能整合到一个服务中
 39 | - **简化接口**: 提供清晰、易用的 API
 40 | - **自动清理**: 内置简化的数据清理机制
 41 |
 42 | ### 3. 直观的消息分类 ✅
 43 |
 44 | **实现位置**: `simple-message-classifier.ts`
 45 |
 46 | - **简化分类**: 减少分类维度，专注核心功能
 47 | - **直观逻辑**: 使用更直观的分类规则
 48 | - **减少配置**: 移除复杂的配置驱动分类
 49 | - **高效处理**: 优化的分类性能
 50 |
 51 | ### 4. 精简的数据访问 ✅
 52 |
 53 | **实现位置**: `repositories/`
 54 |
 55 | - **kebab-case 命名**: 统一的文件命名规范
 56 | - **简化接口**: 移除不必要的抽象层
 57 | - **专注功能**: 每个 Repository 专注核心数据访问
 58 | - **清晰命名**: 使用更清晰的方法和变量命名
 59 |
 60 | ## 配置说明
 61 |
 62 | ### 简化的配置项
 63 |
 64 | ```typescript
 65 | interface WorldStateConfig {
 66 |     MemberUpdateInterval: number;      // 成员信息更新间隔（分钟）
 67 |     ChannelUpdateInterval: number;     // 频道信息更新间隔（分钟）
 68 |     ActiveTurnRetention: number;       // 活跃回合保留时间（小时）
 69 |     MaxActiveTurns: number;           // 最大活跃回合数
 70 |     AutoFoldExpiredTurns: boolean;    // 是否自动折叠过期回合
 71 |     MinMessageLength: number;         // 最小消息长度
 72 |     DataRetentionDays: number;        // 数据保留期限（天）
 73 |     ActiveChannelLimit: number;       // 活跃频道显示限制
 74 |     MaxTurnsPerChannel: number;       // 每个频道显示的最大回合数
 75 |     MaxEventsPerTurn: number;         // 每个回合显示的最大事件数
 76 | }
 77 | ```
 78 |
 79 | ## 使用方法
 80 |
 81 | ### 1. 基本使用
 82 |
 83 | ```typescript
 84 | import { WorldStateService, WorldStateConfig } from "./services/worldstate";
 85 |
 86 | // 创建配置
 87 | const config: WorldStateConfig = {
 88 |     MemberUpdateInterval: 60,
 89 |     ChannelUpdateInterval: 120,
 90 |     ActiveTurnRetention: 24,
 91 |     MaxActiveTurns: 20,
 92 |     AutoFoldExpiredTurns: true,
 93 |     MinMessageLength: 1,
 94 |     DataRetentionDays: 30,
 95 |     ActiveChannelLimit: 10,
 96 |     MaxTurnsPerChannel: 10,
 97 |     MaxEventsPerTurn: 50,
 98 | };
 99 |
100 | // 创建服务
101 | const worldStateService = new WorldStateService(ctx, config);
102 | ```
103 |
104 | ### 2. 处理消息
105 |
106 | ```typescript
107 | // 在消息处理中间件中
108 | await worldStateService.handleMessage(session);
109 | ```
110 |
111 | ### 3. 获取世界状态
112 |
113 | ```typescript
114 | // 获取当前世界状态
115 | const worldState = await worldStateService.getWorldState(allowedChannels);
116 | ```
117 |
118 | ### 4. 手动操作
119 |
120 | ```typescript
121 | // 手动折叠回合
122 | await worldStateService.foldTurn(turnId);
123 |
124 | // 手动生成摘要
125 | await worldStateService.summarizeTurn(turnId);
126 |
127 | // 执行数据清理
128 | const cleanupResult = await worldStateService.performDataCleanup();
129 |
130 | // 获取统计信息
131 | const stats = await worldStateService.getDatabaseStats();
132 | ```
133 |
134 | ## 技术特点
135 |
136 | ### 1. 简化架构
137 | - 减少抽象层级，提高代码可读性
138 | - 统一文件命名规范（kebab-case）
139 | - 专注核心功能，避免过度工程化
140 |
141 | ### 2. 配置驱动
142 | - 扁平化配置结构，易于理解和维护
143 | - 依赖 Koishi 框架的配置管理能力
144 | - 所有功能都可通过配置调整
145 |
146 | ### 3. 性能优化
147 | - 简化的消息分类逻辑
148 | - 高效的数据访问模式
149 | - 内置数据清理机制
150 |
151 | ### 4. 易于维护
152 | - 清晰的代码结构和命名
153 | - 每个类专注单一职责
154 | - 自文档化的代码风格
155 |
156 | ## 数据库表结构
157 |
158 | 模块扩展了以下 Koishi 数据库表：
159 |
160 | - `user`: 用户基础信息
161 | - `channel`: 频道/群组信息
162 | - `members`: 成员信息
163 | - `turns`: 回合数据
164 | - `channel_events`: 频道事件
165 | - `agent_responses`: AI 响应记录
166 |
167 | ## 模板渲染
168 |
169 | 配合 `world_state.mustache` 模板使用，支持：
170 |
171 | - 活跃和非活跃频道分组显示
172 | - 成员信息的智能筛选和摘要
173 | - 回合历史的优化展示
174 | - 事件的结构化渲染
175 |
176 | ## 优化成果
177 |
178 | 相比原始版本，优化后的模块具有以下改进：
179 |
180 | 1. **减少文件数量**: 从 10+ 个文件减少到 7 个核心文件
181 | 2. **简化配置**: 从 5 层嵌套减少到扁平化结构
182 | 3. **统一命名**: 全部采用 kebab-case 文件命名
183 | 4. **减少抽象**: 移除不必要的设计模式和抽象层
184 | 5. **提升性能**: 简化的分类逻辑和数据访问
185 | 6. **易于维护**: 更清晰的代码结构和职责划分

</file_content>

<file_content path="packages/core/src/services/worldstate/simple-message-classifier.ts">
  1 | import { Context, Logger, Session } from "koishi";
  2 | import { WorldStateConfig } from "./config";
  3 |
  4 | /**
  5 |  * 简化的消息分类器
  6 |  * 专注于核心分类功能，减少复杂度
  7 |  */
  8 | export class SimpleMessageClassifier {
  9 |     private logger: Logger;
 10 |
 11 |     constructor(private ctx: Context, private config: WorldStateConfig) {
 12 |         this.logger = ctx.logger("worldstate:classifier");
 13 |     }
 14 |
 15 |     /**
 16 |      * 分类消息
 17 |      */
 18 |     classifyMessage(session: Session): MessageClassification {
 19 |         const messageType = this.getMessageType(session);
 20 |         const shouldStore = this.shouldStoreMessage(session, messageType);
 21 |
 22 |         return {
 23 |             type: messageType,
 24 |             shouldStore,
 25 |             metadata: this.extractBasicMetadata(session),
 26 |         };
 27 |     }
 28 |
 29 |     /**
 30 |      * 获取消息类型
 31 |      */
 32 |     private getMessageType(session: Session): MessageType {
 33 |         // 系统事件
 34 |         if (this.isSystemEvent(session)) {
 35 |             return this.getSystemEventType(session);
 36 |         }
 37 |
 38 |         // 机器人消息
 39 |         if (session.userId === session.selfId) {
 40 |             return "bot_message";
 41 |         }
 42 |
 43 |         // 用户消息
 44 |         return this.getUserMessageType(session);
 45 |     }
 46 |
 47 |     /**
 48 |      * 判断是否为系统事件
 49 |      */
 50 |     private isSystemEvent(session: Session): boolean {
 51 |         const systemEventTypes = ["guild-member-added", "guild-member-removed", "guild-updated", "channel-updated"];
 52 |         return systemEventTypes.includes(session.type);
 53 |     }
 54 |
 55 |     /**
 56 |      * 获取系统事件类型
 57 |      */
 58 |     private getSystemEventType(session: Session): MessageType {
 59 |         switch (session.type) {
 60 |             case "guild-member-added":
 61 |                 return "user_joined";
 62 |             case "guild-member-removed":
 63 |                 return "user_left";
 64 |             case "guild-updated":
 65 |             case "channel-updated":
 66 |                 return "channel_updated";
 67 |             default:
 68 |                 return "system_notification";
 69 |         }
 70 |     }
 71 |
 72 |     /**
 73 |      * 获取用户消息类型
 74 |      */
 75 |     private getUserMessageType(session: Session): MessageType {
 76 |         const content = session.content?.trim();
 77 |
 78 |         if (!content) {
 79 |             return "empty_message";
 80 |         }
 81 |
 82 |         if (content.length < this.config.MinMessageLength) {
 83 |             return "short_message";
 84 |         }
 85 |
 86 |         // 命令消息
 87 |         if (content.startsWith("/") || content.startsWith("!")) {
 88 |             return "command_message";
 89 |         }
 90 |
 91 |         // @消息
 92 |         if (this.isAtMessage(session)) {
 93 |             return "mention_message";
 94 |         }
 95 |
 96 |         // 回复消息
 97 |         if (session.quote) {
 98 |             return "reply_message";
 99 |         }
100 |
101 |         return "user_message";
102 |     }
103 |
104 |     /**
105 |      * 判断是否为@消息
106 |      */
107 |     private isAtMessage(session: Session): boolean {
108 |         return session.stripped?.atSelf || false;
109 |     }
110 |
111 |     /**
112 |      * 判断是否应该存储消息
113 |      */
114 |     private shouldStoreMessage(session: Session, messageType: MessageType): boolean {
115 |         // 不存储空消息和过短消息
116 |         if (messageType === "empty_message" || messageType === "short_message") {
117 |             return false;
118 |         }
119 |
120 |         // 存储所有其他类型的消息
121 |         return true;
122 |     }
123 |
124 |     /**
125 |      * 提取基础元数据
126 |      */
127 |     private extractBasicMetadata(session: Session): MessageMetadata {
128 |         return {
129 |             platform: session.platform || "",
130 |             channelId: session.channelId || "",
131 |             userId: session.userId || "",
132 |             messageId: session.messageId || "",
133 |             timestamp: new Date().toISOString(),
134 |             contentLength: session.content?.length || 0,
135 |             hasQuote: !!session.quote,
136 |             isAtSelf: this.isAtMessage(session),
137 |         };
138 |     }
139 | }
140 |
141 | // 简化的类型定义
142 | export type MessageType =
143 |     | "user_message"
144 |     | "mention_message"
145 |     | "reply_message"
146 |     | "command_message"
147 |     | "user_joined"
148 |     | "user_left"
149 |     | "channel_updated"
150 |     | "system_notification"
151 |     | "bot_message"
152 |     | "empty_message"
153 |     | "short_message";
154 |
155 | export interface MessageClassification {
156 |     type: MessageType;
157 |     shouldStore: boolean;
158 |     metadata: MessageMetadata;
159 | }
160 |
161 | export interface MessageMetadata {
162 |     platform: string;
163 |     channelId: string;
164 |     userId: string;
165 |     messageId: string;
166 |     timestamp: string;
167 |     contentLength: number;
168 |     hasQuote: boolean;
169 |     isAtSelf: boolean;
170 | }

</file_content>

<file_content path="packages/core/src/services/worldstate/world-state-service.ts">
  1 | import { $, Context, randomId, Service, Session } from "koishi";
  2 | import { AgentResponse, Channel, MemberSummary, WorldState } from "./interfaces";
  3 | import { AgentResponseData, ChannelEventData, TurnData } from "./model";
  4 | import { MemberRepository } from "./repositories/member-repository";
  5 | import { TurnRepository } from "./repositories/turn-repository";
  6 | import { WorldStateConfig } from "./config";
  7 |
  8 | import { SimpleMessageClassifier, MessageClassification } from "./simple-message-classifier";
  9 |
 10 | declare module "koishi" {
 11 |     interface Context {
 12 |         "yesimbot.worldState": WorldStateService;
 13 |     }
 14 | }
 15 |
 16 | /**
 17 |  * WorldState 服务
 18 |  * 负责收集、管理和组织群组消息、聊天记录、系统事件，并为 PromptBuilder 提供结构化数据
 19 |  */
 20 | export class WorldStateService extends Service<WorldStateConfig> {
 21 |     private members: MemberRepository;
 22 |     private turns: TurnRepository;
 23 |
 24 |     private messageClassifier: SimpleMessageClassifier;
 25 |     private cleanupTimer?: NodeJS.Timeout;
 26 |
 27 |     constructor(ctx: Context, config: WorldStateConfig) {
 28 |         super(ctx, "yesimbot.worldState", true);
 29 |         this.config = config;
 30 |
 31 |         // 应用所有数据库模型定义
 32 |         ctx.plugin(require("./model"));
 33 |
 34 |         // 初始化 repositories
 35 |         this.members = new MemberRepository(ctx);
 36 |         this.turns = new TurnRepository(ctx, this.members);
 37 |
 38 |         // 初始化功能模块
 39 |         this.messageClassifier = new SimpleMessageClassifier(ctx, this.config);
 40 |
 41 |         // 启动定期清理任务
 42 |         this.startCleanupTask();
 43 |
 44 |         // 注册停止处理
 45 |         ctx.on("dispose", () => {
 46 |             this.stopCleanupTask();
 47 |         });
 48 |     }
 49 |
 50 |     /**
 51 |      * 处理消息事件
 52 |      */
 53 |     async handleMessage(session: Session): Promise<void> {
 54 |         const classification = this.messageClassifier.classifyMessage(session);
 55 |
 56 |         if (!classification.shouldStore) {
 57 |             return;
 58 |         }
 59 |
 60 |         // 获取或创建当前回合
 61 |         const currentTurn = await this.turns.getCurrentTurn(session.platform!, session.channelId!);
 62 |
 63 |         // 存储事件数据
 64 |         await this.storeChannelEvent(currentTurn.id, session, classification);
 65 |
 66 |         // 更新成员信息
 67 |         if (session.userId && classification.type === "system_notification") {
 68 |             await this.updateMemberActivity(session);
 69 |         }
 70 |     }
 71 |
 72 |     /**
 73 |      * 存储频道事件
 74 |      */
 75 |     private async storeChannelEvent(turnId: string, session: Session, classification: MessageClassification): Promise<void> {
 76 |         const eventData: Partial<ChannelEventData> = {
 77 |             turnId,
 78 |             type: classification.type,
 79 |             timestamp: new Date(session.timestamp),
 80 |             data: classification.metadata,
 81 |         };
 82 |
 83 |         await this.ctx.database.create("channel_events", eventData);
 84 |     }
 85 |
 86 |     /**
 87 |      * 更新成员活动信息
 88 |      */
 89 |     private async updateMemberActivity(session: Session): Promise<void> {
 90 |         if (!session.userId || !session.channelId || !session.platform) {
 91 |             return;
 92 |         }
 93 |
 94 |         await this.ctx.database.upsert("members", {
 95 |             userId: session.userId,
 96 |             platform: session.platform,
 97 |             channelId: session.channelId,
 98 |             nick: session.username || session.author?.nick,
 99 |             lastActive: new Date(),
100 |         });
101 |     }
102 |
103 |     /**
104 |      * 获取当前的世界状态
105 |      */
106 |     async getWorldState(allowedChannels: string[]): Promise<WorldState> {
107 |         const activeThreshold = new Date(Date.now() - 1 * 60 * 60 * 1000);
108 |
109 |         const allChannels = await this.ctx.database.get("channel", { id: allowedChannels });
110 |
111 |         const activeChannelPromises: Promise<Channel>[] = [];
112 |         const inactiveChannelPromises: Promise<Channel>[] = [];
113 |
114 |         // 应用渲染配置限制
115 |         let activeCount = 0;
116 |
117 |         for (const chan of allChannels) {
118 |             const promise = this.getFullChannel(chan.platform, chan.id);
119 |             if (chan.lastActivityAt > activeThreshold && activeCount < this.config.ActiveChannelLimit) {
120 |                 activeChannelPromises.push(promise);
121 |                 activeCount++;
122 |             } else {
123 |                 inactiveChannelPromises.push(promise);
124 |             }
125 |         }
126 |
127 |         const [activeChannels, inactiveChannels] = await Promise.all([
128 |             Promise.all(activeChannelPromises),
129 |             Promise.all(inactiveChannelPromises),
130 |         ]);
131 |
132 |         return {
133 |             timestamp: new Date().toISOString(),
134 |             activeChannels,
135 |             inactiveChannels,
136 |         };
137 |     }
138 |
139 |     /**
140 |      * 获取完整的频道信息
141 |      */
142 |     async getFullChannel(platform: string, channelId: string): Promise<Channel> {
143 |         // 获取频道基础信息
144 |         const [channelRecord] = await this.ctx.database.get("channel", { platform, id: channelId });
145 |
146 |         if (!channelRecord) {
147 |             throw new Error(`Channel not found: ${platform}:${channelId}`);
148 |         }
149 |
150 |         // 获取成员信息
151 |         const members = await this.members.getFullMembers(platform, channelId);
152 |
153 |         // 获取成员摘要
154 |         const memberSummary = await this.getMemberSummary(platform, channelId);
155 |
156 |         // 获取回合历史
157 |         const history = await this.getChannelTurns(platform, channelId);
158 |
159 |         return {
160 |             id: channelId,
161 |             name: channelRecord.name || `频道 ${channelId}`,
162 |             type: this.determineChannelType(channelRecord),
163 |             platform,
164 |             meta: {
165 |                 description: channelRecord.description,
166 |             },
167 |             members,
168 |             memberSummary,
169 |             history,
170 |         };
171 |     }
172 |
173 |     /**
174 |      * 获取成员摘要信息
175 |      */
176 |     private async getMemberSummary(platform: string, channelId: string): Promise<MemberSummary> {
177 |         const recentThreshold = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
178 |
179 |         const [totalCount, recentActiveCount] = await Promise.all([
180 |             this.ctx.database
181 |                 .select("members")
182 |                 .where({ platform, channelId })
183 |                 .execute()
184 |                 .then((r) => r.length),
185 |             this.ctx.database
186 |                 .select("members")
187 |                 .where({
188 |                     platform,
189 |                     channelId,
190 |                     lastActive: { $gte: recentThreshold },
191 |                 })
192 |                 .execute()
193 |                 .then((r) => r.length),
194 |         ]);
195 |
196 |         return {
197 |             total_count: totalCount,
198 |             online_count: 0, // 暂时无法获取在线状态
199 |             recent_active_members_count: recentActiveCount,
200 |         };
201 |     }
202 |
203 |     /**
204 |      * 确定频道类型
205 |      */
206 |     private determineChannelType(channelRecord: any): "guild" | "private" | "sandbox" {
207 |         if (channelRecord.type === "text") return "guild";
208 |         if (channelRecord.type === "direct") return "private";
209 |         return "sandbox";
210 |     }
211 |
212 |     /**
213 |      * 存储 AI 响应
214 |      */
215 |     async storeAgentResponse(turnId: string, response: AgentResponse): Promise<void> {
216 |         const responseData: Partial<AgentResponseData> = {
217 |             turnId,
218 |             thoughts: response.thoughts,
219 |             actions: response.actions,
220 |             observations: response.observations,
221 |         };
222 |
223 |         await this.ctx.database.create("agent_responses", responseData);
224 |     }
225 |
226 |     /**
227 |      * 获取频道的回合历史
228 |      */
229 |     private async getChannelTurns(platform: string, channelId: string): Promise<any[]> {
230 |         const turns = await this.ctx.database
231 |             .select("turns")
232 |             .where({ platform, channelId })
233 |             .orderBy("startTimestamp", "desc")
234 |             .limit(this.config.MaxTurnsPerChannel)
235 |             .execute();
236 |
237 |         return Promise.all(turns.map((turn) => this.buildTurnWithEvents(turn)));
238 |     }
239 |
240 |     /**
241 |      * 构建包含事件的回合对象
242 |      */
243 |     private async buildTurnWithEvents(turnData: TurnData): Promise<any> {
244 |         const events = await this.ctx.database
245 |             .select("channel_events")
246 |             .where({ turnId: turnData.id })
247 |             .orderBy("timestamp", "asc")
248 |             .limit(this.config.MaxEventsPerTurn)
249 |             .execute();
250 |
251 |         return {
252 |             id: turnData.id,
253 |             status: turnData.status,
254 |             summary: turnData.summary,
255 |             events: events.map((event) => ({
256 |                 id: event.id,
257 |                 type: event.type,
258 |                 timestamp: event.timestamp,
259 |                 content: event.content,
260 |                 ...event.data,
261 |             })),
262 |             responses: [], // 简化处理，如需要可以后续添加
263 |         };
264 |     }
265 |
266 |     /**
267 |      * 手动折叠回合
268 |      */
269 |     async foldTurn(turnId: string): Promise<void> {
270 |         await this.ctx.database.upsert("turns", [{ id: turnId, status: "folded" }]);
271 |     }
272 |
273 |     /**
274 |      * 手动生成摘要
275 |      */
276 |     async summarizeTurn(turnId: string): Promise<void> {
277 |         const events = await this.ctx.database.get("channel_events", { turnId });
278 |         const summary = `包含 ${events.length} 个事件`;
279 |         await this.ctx.database.upsert("turns", [{ id: turnId, status: "summarized", summary }]);
280 |     }
281 |
282 |     /**
283 |      * 启动清理任务
284 |      */
285 |     private startCleanupTask(): void {
286 |         const intervalMs = 24 * 60 * 60 * 1000; // 24小时
287 |
288 |         this.cleanupTimer = setInterval(async () => {
289 |             try {
290 |                 await this.performDataCleanup();
291 |             } catch (error) {
292 |                 this.ctx.logger("worldstate").error("数据清理失败:", error);
293 |             }
294 |         }, intervalMs);
295 |     }
296 |
297 |     /**
298 |      * 停止清理任务
299 |      */
300 |     private stopCleanupTask(): void {
301 |         if (this.cleanupTimer) {
302 |             clearInterval(this.cleanupTimer);
303 |             this.cleanupTimer = undefined;
304 |         }
305 |     }
306 |
307 |     /**
308 |      * 执行数据清理
309 |      */
310 |     async performDataCleanup(): Promise<any> {
311 |         const retentionDate = new Date(Date.now() - this.config.DataRetentionDays * 24 * 60 * 60 * 1000);
312 |
313 |         // 清理过期回合及相关数据
314 |         const expiredTurns = await this.ctx.database
315 |             .select("turns")
316 |             .where((row) => $.and($.lt(row.endTimestamp, retentionDate)))
317 |             .execute();
318 |
319 |         if (expiredTurns.length > 0) {
320 |             const turnIds = expiredTurns.map((t) => t.id);
321 |
322 |             // 删除相关数据
323 |             await Promise.all([
324 |                 this.ctx.database.remove("channel_events", { turnId: turnIds }),
325 |                 this.ctx.database.remove("agent_responses", { turnId: turnIds }),
326 |                 this.ctx.database.remove("turns", { id: turnIds }),
327 |             ]);
328 |         }
329 |
330 |         return { deletedTurns: expiredTurns.length };
331 |     }
332 |
333 |     /**
334 |      * 获取数据库统计信息
335 |      */
336 |     async getDatabaseStats(): Promise<any> {
337 |         const [turnCount, eventCount, memberCount] = await Promise.all([
338 |             this.ctx.database
339 |                 .select("turns")
340 |                 .execute()
341 |                 .then((r) => r.length),
342 |             this.ctx.database
343 |                 .select("channel_events")
344 |                 .execute()
345 |                 .then((r) => r.length),
346 |             this.ctx.database
347 |                 .select("members")
348 |                 .execute()
349 |                 .then((r) => r.length),
350 |         ]);
351 |
352 |         return {
353 |             turns: turnCount,
354 |             events: eventCount,
355 |             members: memberCount,
356 |         };
357 |     }
358 |
359 |     /**
360 |      * 更新频道成员数量
361 |      */
362 |     async updateChannelMemberCount(channelId: string, platform: string): Promise<void> {
363 |         const memberCount = await this.ctx.database
364 |             .select("members")
365 |             .where({ platform, channelId })
366 |             .execute()
367 |             .then((r) => r.length);
368 |
369 |         await this.ctx.database.upsert("channel", [
370 |             {
371 |                 id: channelId,
372 |                 platform,
373 |                 memberCount,
374 |             },
375 |         ]);
376 |     }
377 | }

</file_content>
</file_content>
