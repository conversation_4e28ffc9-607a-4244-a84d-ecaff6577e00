import { Context } from "koishi";
import { FlowAnalysis } from "./conversation-flow-analyzer";
import { Channel, Turn } from "../services/worldstate";

// --- 配置接口 ---
// 从旧的 ReplyConditionConfig 中提取和简化
export interface WillingnessConfig {
    baseThreshold: number; // 基础意愿阈值
    mentionBoost: number; // 被@时的意愿提升
    keywordBoost: number; // 关键词意愿提升
    silenceBoostFactor: number; // 沉默时间越长，提升因子越大
    fastPacePenalty: number; // 对话节奏过快时的意愿惩罚
    lateNightDampening: { startHour: number; endHour: number; factor: number }; // 深夜抑制因子
    keywords: string[];
}

// --- 输出接口 ---
export interface Willingness {
    toEngage: number; // 最终的参与意愿 (0-1)
    threshold: number; // 动态计算出的、本次决策所需的阈值
    reasons: string[]; // 做出此意愿判断的理由，供 LLM 参考
}

/**
 * 意愿值计算器 (Willingness Calculator)
 * 这是一个普通的类，由 Agent 主服务进行实例化和管理。
 */
export class WillingnessCalculator {
    // 构造函数接收配置和可能需要的 Koishi 上下文
    constructor(private config: WillingnessConfig, private ctx: Context) {}

    /**
     * 计算 Agent 在当前情境下的行动意愿。
     * @param flowAnalysis 对话流分析结果
     * @param turn 当前回合
     * @param channel 当前频道
     * @returns 结构化的意愿对象
     */
    public calculate(flowAnalysis: FlowAnalysis, turn: Turn, channel: Channel): Willingness {
        let baseWillingness = 0.2; // 设定一个非常低的基础意愿值
        const reasons: string[] = [`基础意愿值为 ${baseWillingness.toFixed(2)}`];
        let threshold = this.config.baseThreshold;

        // 1. 基于对话流的调整
        if (flowAnalysis.pace === "fast") {
            baseWillingness -= this.config.fastPacePenalty;
            reasons.push(`对话节奏过快，意愿 -${this.config.fastPacePenalty}`);
        }
        if (flowAnalysis.silenceSinceLastTurnMs > 30 * 1000) {
            // 超过30秒沉默
            const boost = Math.min(0.5, (flowAnalysis.silenceSinceLastTurnMs / (5 * 60 * 1000)) * this.config.silenceBoostFactor);
            baseWillingness += boost;
            reasons.push(`出现 ${Math.round(flowAnalysis.silenceSinceLastTurnMs / 1000)}s 沉默，意愿 +${boost.toFixed(2)}`);
        }

        // 2. 基于事件内容的调整
        const lastEvent = turn.events[turn.events.length - 1];
        if (lastEvent) {
            const content = (lastEvent.payload as any).content?.toLowerCase() || "";

            // 检查@
            if (content.includes("@" + this.ctx.bots[0]?.selfId) || content.includes("@letta")) {
                baseWillingness += this.config.mentionBoost;
                threshold *= 0.2; // 被@时，大幅降低触发门槛
                reasons.push(`Agent被直接提及，意愿 +${this.config.mentionBoost}，阈值大幅降低`);
            }

            // 检查关键词
            const matchedKeyword = this.config.keywords.find((kw) => content.includes(kw.toLowerCase()));
            if (matchedKeyword) {
                baseWillingness += this.config.keywordBoost;
                reasons.push(`消息包含关键词 "${matchedKeyword}"，意愿 +${this.config.keywordBoost}`);
            }
        }

        // 3. 基于时间的调整（例如深夜）
        const hour = new Date().getHours();
        const { startHour, endHour, factor } = this.config.lateNightDampening;
        if (hour >= startHour || hour < endHour) {
            baseWillingness *= factor;
            reasons.push(`处于深夜时段，意愿 x${factor}`);
        }

        // 4. 基于消息累积的调整（从旧代码的 WillingnessManager 演变而来）
        const messageCount = turn.events.filter((e) => e.type === "message").length;
        if (messageCount > 1) {
            const accumulationBoost = (1 / (1 + Math.exp(-(messageCount - 3)))) * 0.5; // 使用 sigmoid 函数平滑增长
            baseWillingness += accumulationBoost;
            reasons.push(`${messageCount}条消息累积，意愿 +${accumulationBoost.toFixed(2)}`);
        }

        // 将最终意愿值限制在 0-1 之间
        const finalWillingness = Math.max(0, Math.min(1, baseWillingness));

        return {
            toEngage: finalWillingness,
            threshold,
            reasons,
        };
    }
}
