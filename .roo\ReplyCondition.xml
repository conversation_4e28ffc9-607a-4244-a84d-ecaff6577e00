<task>
'packages/core/src/shared/utils/conversation-flow-analyzer.ts' (see below for file content)
'packages/core/src/middleware/condition.middleware.ts' (see below for file content)
</task>

<file_content path="packages/core/src/shared/utils/conversation-flow-analyzer.ts">
  1 | import { Context } from "koishi";
  2 | import { ChatMessage } from "../database";
  3 |
  4 | // 消息关联类型
  5 | export type MessageRelationType =
  6 |     | "topic_continuation" // 话题延续
  7 |     | "topic_shift" // 话题转移
  8 |     | "response_to_previous" // 回应之前的消息
  9 |     | "new_topic" // 全新话题
 10 |     | "side_conversation"; // 旁支对话
 11 |
 12 | // 话题状态
 13 | export type TopicStatus =
 14 |     | "developing" // 正在发展
 15 |     | "stable" // 稳定讨论
 16 |     | "cooling" // 逐渐冷却
 17 |     | "ended"; // 已结束
 18 |
 19 | // 消息分析结果
 20 | export interface MessageAnalysis {
 21 |     messageId: string;
 22 |     relationType: MessageRelationType;
 23 |     topicId?: string;
 24 |     referencedMessageIds: string[];
 25 |     confidence: number; // 0-1，分析的置信度
 26 |     timestamp: Date;
 27 | }
 28 |
 29 | // 话题分析结果
 30 | export interface TopicAnalysis {
 31 |     topicId: string;
 32 |     status: TopicStatus;
 33 |     participants: Set<string>;
 34 |     lastActivity: Date;
 35 |     messageCount: number;
 36 |     keywords: string[];
 37 |     stability: number; // 话题稳定性 0-1
 38 | }
 39 |
 40 | // 对话流状态
 41 | export interface ConversationFlow {
 42 |     activeTopics: Map<string, TopicAnalysis>;
 43 |     recentMessages: MessageAnalysis[];
 44 |     conversationPace: "fast" | "normal" | "slow";
 45 |     lastAnalysisTime: Date;
 46 | }
 47 |
 48 | // 回复决策结果
 49 | export interface ReplyDecision {
 50 |     shouldReply: boolean;
 51 |     reason: string;
 52 |     confidence: number;
 53 |     suggestedWaitTime?: number;
 54 | }
 55 |
 56 | export class ConversationFlowAnalyzer {
 57 |     private flows = new Map<string, ConversationFlow>();
 58 |     private readonly maxRecentMessages = 15;
 59 |     private readonly topicTimeoutMs = 10 * 60 * 1000; // 10分钟话题超时
 60 |
 61 |     constructor(private ctx: Context) {}
 62 |
 63 |     /**
 64 |      * 分析新消息并更新对话流
 65 |      */
 66 |     public async analyzeMessage(channelId: string, message: ChatMessage): Promise<MessageAnalysis> {
 67 |         let flow = this.flows.get(channelId);
 68 |         if (!flow) {
 69 |             flow = {
 70 |                 activeTopics: new Map(),
 71 |                 recentMessages: [],
 72 |                 conversationPace: "normal",
 73 |                 lastAnalysisTime: new Date(),
 74 |             };
 75 |             this.flows.set(channelId, flow);
 76 |         }
 77 |
 78 |         // 分析消息关联性
 79 |         const analysis = await this.analyzeMessageRelation(message, flow);
 80 |
 81 |         // 更新对话流状态
 82 |         this.updateConversationFlow(flow, analysis, message);
 83 |
 84 |         // 清理过期话题
 85 |         this.cleanupExpiredTopics(flow);
 86 |
 87 |         return analysis;
 88 |     }
 89 |
 90 |     /**
 91 |      * 判断是否适合回复
 92 |      */
 93 |     public shouldReply(channelId: string, currentMessage: ChatMessage): ReplyDecision {
 94 |         const flow = this.flows.get(channelId);
 95 |         if (!flow) {
 96 |             return { shouldReply: false, reason: "no_flow_data", confidence: 0 };
 97 |         }
 98 |
 99 |         // 如果被@，立即回复
100 |         if (this.isDirectMention(currentMessage)) {
101 |             return {
102 |                 shouldReply: true,
103 |                 reason: "direct_mention",
104 |                 confidence: 1.0,
105 |                 suggestedWaitTime: 1000, // 1秒快速响应
106 |             };
107 |         }
108 |
109 |         // 分析话题状态
110 |         const topicAnalysis = this.analyzeTopicReadiness(flow);
111 |
112 |         // 分析对话节奏
113 |         const paceAnalysis = this.analyzePaceReadiness(flow);
114 |
115 |         // 综合判断
116 |         const confidence = (topicAnalysis.confidence + paceAnalysis.confidence) / 2;
117 |         const shouldReply = confidence > 0.6;
118 |
119 |         // 计算建议等待时间
120 |         const suggestedWaitTime = this.calculateSuggestedWaitTime(flow, topicAnalysis, paceAnalysis);
121 |
122 |         return {
123 |             shouldReply,
124 |             reason: shouldReply ? topicAnalysis.reason : "topic_still_developing",
125 |             confidence,
126 |             suggestedWaitTime,
127 |         };
128 |     }
129 |
130 |     /**
131 |      * 分析消息关联性
132 |      */
133 |     private async analyzeMessageRelation(message: ChatMessage, flow: ConversationFlow): Promise<MessageAnalysis> {
134 |         const recentMessages = flow.recentMessages.slice(-8); // 分析最近8条消息
135 |
136 |         // 提取关键词
137 |         const keywords = this.extractKeywords(message.content as string);
138 |         let bestMatch: { topicId?: string; confidence: number; type: MessageRelationType } = {
139 |             confidence: 0,
140 |             type: "new_topic",
141 |         };
142 |
143 |         // 检查是否与现有话题相关
144 |         for (const [topicId, topic] of flow.activeTopics) {
145 |             const similarity = this.calculateTopicSimilarity(keywords, topic.keywords);
146 |             if (similarity > bestMatch.confidence) {
147 |                 bestMatch = {
148 |                     topicId,
149 |                     confidence: similarity,
150 |                     type: similarity > 0.7 ? "topic_continuation" : "topic_shift",
151 |                 };
152 |             }
153 |         }
154 |
155 |         // 检查是否回应之前的消息
156 |         const referencedMessages = this.findReferencedMessages(message, recentMessages);
157 |         if (referencedMessages.length > 0 && bestMatch.confidence < 0.8) {
158 |             bestMatch = {
159 |                 confidence: 0.8,
160 |                 type: "response_to_previous",
161 |             };
162 |         }
163 |
164 |         return {
165 |             messageId: message.messageId,
166 |             relationType: bestMatch.type,
167 |             topicId: bestMatch.topicId,
168 |             referencedMessageIds: referencedMessages,
169 |             confidence: bestMatch.confidence,
170 |             timestamp: message.timestamp,
171 |         };
172 |     }
173 |
174 |     /**
175 |      * 更新对话流状态
176 |      */
177 |     private updateConversationFlow(flow: ConversationFlow, analysis: MessageAnalysis, message: ChatMessage): void {
178 |         // 添加到最近消息
179 |         flow.recentMessages.push(analysis);
180 |         if (flow.recentMessages.length > this.maxRecentMessages) {
181 |             flow.recentMessages.shift();
182 |         }
183 |
184 |         // 更新话题状态
185 |         if (analysis.topicId) {
186 |             const topic = flow.activeTopics.get(analysis.topicId);
187 |             if (topic) {
188 |                 topic.lastActivity = analysis.timestamp;
189 |                 topic.messageCount++;
190 |                 topic.participants.add(message.sender.id);
191 |                 topic.status = this.determineTopicStatus(topic, flow.recentMessages);
192 |
193 |                 // 更新关键词
194 |                 const newKeywords = this.extractKeywords(message.content as string);
195 |                 topic.keywords = [...new Set([...topic.keywords, ...newKeywords])].slice(0, 10);
196 |             }
197 |         } else if (analysis.relationType === "new_topic") {
198 |             // 创建新话题
199 |             const newTopicId = `topic_${Date.now()}_${message.sender.id}`;
200 |             const keywords = this.extractKeywords(message.content as string);
201 |
202 |             flow.activeTopics.set(newTopicId, {
203 |                 topicId: newTopicId,
204 |                 status: "developing",
205 |                 participants: new Set([message.sender.id]),
206 |                 lastActivity: analysis.timestamp,
207 |                 messageCount: 1,
208 |                 keywords: keywords,
209 |                 stability: 0.1,
210 |             });
211 |         }
212 |
213 |         // 更新对话节奏
214 |         flow.conversationPace = this.calculateConversationPace(flow.recentMessages);
215 |         flow.lastAnalysisTime = new Date();
216 |     }
217 |
218 |     /**
219 |      * 分析话题准备状态
220 |      */
221 |     private analyzeTopicReadiness(flow: ConversationFlow): { confidence: number; reason: string } {
222 |         const activeTopics = Array.from(flow.activeTopics.values());
223 |
224 |         // 如果没有活跃话题，可以回复
225 |         if (activeTopics.length === 0) {
226 |             return { confidence: 0.8, reason: "no_active_topics" };
227 |         }
228 |
229 |         // 检查话题是否稳定或冷却
230 |         const stableTopics = activeTopics.filter((t) => t.status === "stable" || t.status === "cooling");
231 |         if (stableTopics.length > 0) {
232 |             return { confidence: 0.7, reason: "topics_stable" };
233 |         }
234 |
235 |         // 检查话题是否已经有足够的讨论
236 |         const matureTopics = activeTopics.filter((t) => t.messageCount >= 3);
237 |         if (matureTopics.length > 0) {
238 |             return { confidence: 0.6, reason: "topics_mature" };
239 |         }
240 |
241 |         return { confidence: 0.2, reason: "topics_developing" };
242 |     }
243 |
244 |     /**
245 |      * 分析节奏准备状态
246 |      */
247 |     private analyzePaceReadiness(flow: ConversationFlow): { confidence: number; reason: string } {
248 |         const recentMessages = flow.recentMessages.slice(-5);
249 |         if (recentMessages.length < 2) {
250 |             return { confidence: 0.5, reason: "insufficient_data" };
251 |         }
252 |
253 |         // 计算消息间隔
254 |         const intervals = [];
255 |         for (let i = 1; i < recentMessages.length; i++) {
256 |             const interval = recentMessages[i].timestamp.getTime() - recentMessages[i - 1].timestamp.getTime();
257 |             intervals.push(interval);
258 |         }
259 |
260 |         const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
261 |
262 |         // 如果最近的消息间隔较大，说明对话节奏放缓，适合回复
263 |         if (avgInterval > 30000) {
264 |             // 30秒
265 |             return { confidence: 0.8, reason: "conversation_slowing" };
266 |         }
267 |
268 |         // 如果节奏很快，等待一下
269 |         if (avgInterval < 5000) {
270 |             // 5秒
271 |             return { confidence: 0.2, reason: "conversation_too_fast" };
272 |         }
273 |
274 |         return { confidence: 0.5, reason: "normal_pace" };
275 |     }
276 |
277 |     /**
278 |      * 计算建议等待时间
279 |      */
280 |     private calculateSuggestedWaitTime(
281 |         flow: ConversationFlow,
282 |         topicAnalysis: { confidence: number; reason: string },
283 |         paceAnalysis: { confidence: number; reason: string }
284 |     ): number {
285 |         let baseWaitTime = 3000; // 基础3秒
286 |
287 |         // 根据话题状态调整
288 |         switch (topicAnalysis.reason) {
289 |             case "topics_developing":
290 |                 baseWaitTime *= 2.0; // 话题发展中，延长等待
291 |                 break;
292 |             case "topics_stable":
293 |                 baseWaitTime *= 0.8; // 话题稳定，可以适当缩短
294 |                 break;
295 |             case "no_active_topics":
296 |                 baseWaitTime *= 0.6; // 无活跃话题，可以更快回复
297 |                 break;
298 |         }
299 |
300 |         // 根据节奏调整
301 |         switch (paceAnalysis.reason) {
302 |             case "conversation_too_fast":
303 |                 baseWaitTime *= 2.5; // 对话太快，大幅延长
304 |                 break;
305 |             case "conversation_slowing":
306 |                 baseWaitTime *= 0.5; // 对话放缓，可以更快插入
307 |                 break;
308 |         }
309 |
310 |         return Math.max(1000, Math.min(baseWaitTime, 8000)); // 限制在1-8秒
311 |     }
312 |
313 |     /**
314 |      * 提取关键词
315 |      */
316 |     private extractKeywords(content: string): string[] {
317 |         // 简化的关键词提取
318 |         const cleanContent = content
319 |             .toLowerCase()
320 |             .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, " ")
321 |             .replace(/\s+/g, " ")
322 |             .trim();
323 |
324 |         const words = cleanContent
325 |             .split(" ")
326 |             .filter((word) => word.length > 1)
327 |             .filter((word) => !this.isStopWord(word));
328 |
329 |         return [...new Set(words)].slice(0, 5);
330 |     }
331 |
332 |     /**
333 |      * 判断是否为停用词
334 |      */
335 |     private isStopWord(word: string): boolean {
336 |         const stopWords = new Set([
337 |             "的",
338 |             "了",
339 |             "是",
340 |             "在",
341 |             "我",
342 |             "你",
343 |             "他",
344 |             "她",
345 |             "它",
346 |             "这",
347 |             "那",
348 |             "有",
349 |             "和",
350 |             "与",
351 |             "就",
352 |             "都",
353 |             "要",
354 |             "会",
355 |             "能",
356 |             "可以",
357 |             "不是",
358 |             "the",
359 |             "a",
360 |             "an",
361 |             "and",
362 |             "or",
363 |             "but",
364 |             "in",
365 |             "on",
366 |             "at",
367 |             "to",
368 |             "for",
369 |             "of",
370 |             "with",
371 |             "by",
372 |             "is",
373 |             "are",
374 |             "was",
375 |             "were",
376 |             "be",
377 |         ]);
378 |         return stopWords.has(word);
379 |     }
380 |
381 |     /**
382 |      * 计算话题相似度
383 |      */
384 |     private calculateTopicSimilarity(keywords1: string[], keywords2: string[]): number {
385 |         if (keywords1.length === 0 || keywords2.length === 0) return 0;
386 |
387 |         const set1 = new Set(keywords1);
388 |         const set2 = new Set(keywords2);
389 |         const intersection = new Set([...set1].filter((k) => set2.has(k)));
390 |         const union = new Set([...set1, ...set2]);
391 |
392 |         return intersection.size / union.size;
393 |     }
394 |
395 |     /**
396 |      * 查找引用的消息
397 |      */
398 |     private findReferencedMessages(message: ChatMessage, recentMessages: MessageAnalysis[]): string[] {
399 |         const content = message.content as string;
400 |         const replyKeywords = ["回复", "回应", "@", "刚才", "上面", "之前", "刚刚"];
401 |
402 |         if (replyKeywords.some((keyword) => content.includes(keyword))) {
403 |             return recentMessages.slice(-3).map((m) => m.messageId);
404 |         }
405 |
406 |         return [];
407 |     }
408 |
409 |     /**
410 |      * 确定话题状态
411 |      */
412 |     private determineTopicStatus(topic: TopicAnalysis, recentMessages: MessageAnalysis[]): TopicStatus {
413 |         const now = Date.now();
414 |         const timeSinceLastActivity = now - topic.lastActivity.getTime();
415 |
416 |         // 超过10分钟，话题结束
417 |         if (timeSinceLastActivity > 10 * 60 * 1000) {
418 |             return "ended";
419 |         }
420 |
421 |         // 超过5分钟没有相关消息，话题冷却
422 |         if (timeSinceLastActivity > 5 * 60 * 1000) {
423 |             return "cooling";
424 |         }
425 |
426 |         // 根据消息数量和参与者判断
427 |         if (topic.messageCount >= 5 && topic.participants.size >= 2) {
428 |             return "stable";
429 |         }
430 |
431 |         return "developing";
432 |     }
433 |
434 |     /**
435 |      * 计算对话节奏
436 |      */
437 |     private calculateConversationPace(recentMessages: MessageAnalysis[]): "fast" | "normal" | "slow" {
438 |         if (recentMessages.length < 3) return "normal";
439 |
440 |         const intervals = [];
441 |         for (let i = 1; i < Math.min(recentMessages.length, 6); i++) {
442 |             const interval = recentMessages[i].timestamp.getTime() - recentMessages[i - 1].timestamp.getTime();
443 |             intervals.push(interval);
444 |         }
445 |
446 |         const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
447 |
448 |         if (avgInterval < 10000) return "fast"; // 10秒内
449 |         if (avgInterval > 60000) return "slow"; // 1分钟以上
450 |         return "normal";
451 |     }
452 |
453 |     /**
454 |      * 检查是否直接提及
455 |      */
456 |     private isDirectMention(message: ChatMessage): boolean {
457 |         const content = message.content as string;
458 |         // 简化的@检测，实际应该检查具体的@目标
459 |         return (
460 |             content.includes("@") ||
461 |             content.includes("机器人") ||
462 |             content.includes("bot") ||
463 |             content.includes("AI") ||
464 |             content.includes("助手")
465 |         );
466 |     }
467 |
468 |     /**
469 |      * 清理过期话题
470 |      */
471 |     private cleanupExpiredTopics(flow: ConversationFlow): void {
472 |         const now = Date.now();
473 |         for (const [topicId, topic] of flow.activeTopics) {
474 |             if (now - topic.lastActivity.getTime() > this.topicTimeoutMs) {
475 |                 flow.activeTopics.delete(topicId);
476 |             }
477 |         }
478 |     }
479 |
480 |     /**
481 |      * 获取对话流状态
482 |      */
483 |     public getConversationFlow(channelId: string): ConversationFlow | null {
484 |         return this.flows.get(channelId) || null;
485 |     }
486 |
487 |     /**
488 |      * 获取调试信息
489 |      */
490 |     public getDebugInfo(channelId: string): any {
491 |         const flow = this.flows.get(channelId);
492 |         if (!flow) return null;
493 |
494 |         return {
495 |             activeTopicsCount: flow.activeTopics.size,
496 |             recentMessagesCount: flow.recentMessages.length,
497 |             conversationPace: flow.conversationPace,
498 |             topics: Array.from(flow.activeTopics.values()).map((topic) => ({
499 |                 id: topic.topicId,
500 |                 status: topic.status,
501 |                 messageCount: topic.messageCount,
502 |                 participantsCount: topic.participants.size,
503 |                 keywords: topic.keywords,
504 |             })),
505 |         };
506 |     }
507 | }

</file_content>

<file_content path="packages/core/src/middleware/condition.middleware.ts">
  1 | import { Context } from "koishi";
  2 | import { BaseMiddleware, MiddlewareContext } from "./base";
  3 | import { ChatMessage, ConversationFlowAnalyzer, getChannelType, ReplyDecision } from "../shared";
  4 |
  5 | /**
  6 |  * 回复条件中间件配置
  7 |  */
  8 | export interface ReplyConditionConfig {
  9 |     Channels: string[][];
 10 |     TestMode?: boolean;
 11 |     Strategies: {
 12 |         AtMention: {
 13 |             Enabled: boolean;
 14 |             Probability: number;
 15 |         };
 16 |         Threshold: {
 17 |             Enabled: boolean;
 18 |             Value: number;
 19 |         };
 20 |         ConversationFlow: {
 21 |             Enabled: boolean;
 22 |             ConfidenceThreshold: number;
 23 |         };
 24 |     };
 25 |     Timing: {
 26 |         WaitTime: number;
 27 |         SameUserThreshold: number;
 28 |     };
 29 |     Advanced?: {
 30 |         Willingness?: {
 31 |             MessageIncrease: number;
 32 |             AtIncrease: number;
 33 |             DecayRate: number;
 34 |             RetentionAfterReply: number;
 35 |             Keywords?: {
 36 |                 List: string[];
 37 |                 Increase: number;
 38 |             };
 39 |         };
 40 |     };
 41 | }
 42 |
 43 | /**
 44 |  * 意愿值管理器
 45 |  */
 46 | class WillingnessManager {
 47 |     private willingnessMap = new Map<string, number>();
 48 |     private lastDecayTime = Date.now();
 49 |
 50 |     constructor(private config: ReplyConditionConfig["Advanced"]) {}
 51 |
 52 |     /**
 53 |      * 增加意愿值
 54 |      */
 55 |     increaseWillingness(channelId: string, amount: number): void {
 56 |         const current = this.willingnessMap.get(channelId) || 0;
 57 |         this.willingnessMap.set(channelId, Math.min(100, current + amount));
 58 |     }
 59 |
 60 |     /**
 61 |      * 获取当前意愿值
 62 |      */
 63 |     getWillingness(channelId: string): number {
 64 |         this.decayWillingness();
 65 |         return this.willingnessMap.get(channelId) || 0;
 66 |     }
 67 |
 68 |     /**
 69 |      * 回复后保留意愿值
 70 |      */
 71 |     retainAfterReply(channelId: string): void {
 72 |         const current = this.willingnessMap.get(channelId) || 0;
 73 |         const retention = this.config?.Willingness?.RetentionAfterReply || 0.3;
 74 |         this.willingnessMap.set(channelId, current * retention);
 75 |     }
 76 |
 77 |     /**
 78 |      * 意愿值衰减
 79 |      */
 80 |     private decayWillingness(): void {
 81 |         const now = Date.now();
 82 |         const elapsed = now - this.lastDecayTime;
 83 |         const minutes = elapsed / (60 * 1000);
 84 |
 85 |         if (minutes >= 1) {
 86 |             const decayRate = this.config?.Willingness?.DecayRate || 2;
 87 |
 88 |             for (const [channelId, willingness] of this.willingnessMap) {
 89 |                 const newWillingness = Math.max(0, willingness - decayRate * minutes);
 90 |                 this.willingnessMap.set(channelId, newWillingness);
 91 |             }
 92 |
 93 |             this.lastDecayTime = now;
 94 |         }
 95 |     }
 96 | }
 97 |
 98 | export class ReplyConditionMiddleware extends BaseMiddleware<ReplyConditionConfig> {
 99 |     private willingnessManager: WillingnessManager;
100 |     private lastMessageTimes = new Map<string, number>();
101 |     private flowAnalyzer: ConversationFlowAnalyzer;
102 |
103 |     constructor(ctx: Context, config: ReplyConditionConfig) {
104 |         super("", ctx, config);
105 |         this.willingnessManager = new WillingnessManager(config.Advanced);
106 |         this.flowAnalyzer = new ConversationFlowAnalyzer(ctx);
107 |     }
108 |
109 |     async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
110 |         const startTime = Date.now();
111 |         const channelId = ctx.koishiSession.channelId;
112 |         const userId = ctx.koishiSession.userId;
113 |
114 |         // 简洁的日志标题
115 |         this.logger.info(`📋 回复条件检查 - 频道: ${channelId} | 用户: ${userId}`);
116 |
117 |         try {
118 |             // 1. 基础频道检查
119 |             if (!this.checkChannelPermission(ctx)) {
120 |                 this.logger.warn(`   ❌ 频道不在允许列表中 (配置频道: ${JSON.stringify(this.config.Channels)})`);
121 |                 return;
122 |             }
123 |             this.logger.info(`   ✅ 频道权限检查通过`);
124 |
125 |             // 2. 测试模式检查
126 |             if (this.config.TestMode) {
127 |                 this.logger.info(`   🧪 测试模式：强制回复`);
128 |                 await next();
129 |                 return;
130 |             }
131 |
132 |             // 3. 时间控制检查
133 |             if (!this.checkTiming(ctx)) {
134 |                 this.logger.info(`   ⏸️ 时间控制：跳过回复`);
135 |                 return;
136 |             }
137 |             this.logger.info(`   ✅ 时间控制检查通过`);
138 |
139 |             // 4. 回复策略检查
140 |             this.logger.info(`   🔍 开始评估回复策略...`);
141 |             const shouldReply = await this.evaluateReplyStrategies(ctx);
142 |
143 |             if (shouldReply) {
144 |                 this.logger.info(`   🎉 满足回复条件，准备回复`);
145 |
146 |                 await next();
147 |
148 |                 // 回复后处理意愿值
149 |                 this.willingnessManager.retainAfterReply(channelId);
150 |                 this.logger.info(`   🔄 回复完成，意愿值已更新`);
151 |             } else {
152 |                 this.logger.info(`   🚫 不满足回复条件`);
153 |             }
154 |         } finally {
155 |             const duration = Date.now() - startTime;
156 |             this.logger.info(`   ⏱️ 处理完成，耗时 ${duration}ms`);
157 |             this.logger.info(`──────────────────────────`);
158 |         }
159 |     }
160 |
161 |
162 |     /**
163 |      * 检查频道权限
164 |      */
165 |     private checkChannelPermission(ctx: MiddlewareContext): boolean {
166 |         const channelId = ctx.koishiSession.channelId;
167 |         return this.config.Channels.some((slots) => slots.includes(channelId));
168 |     }
169 |
170 |     /**
171 |      * 检查时间控制
172 |      */
173 |     private checkTiming(ctx: MiddlewareContext): boolean {
174 |         const now = Date.now();
175 |         const channelId = ctx.koishiSession.channelId;
176 |         const userId = ctx.koishiSession.userId;
177 |         const userChannelKey = `${userId}:${channelId}`;
178 |
179 |         // 检查等待时间
180 |         const lastMessageTime = this.lastMessageTimes.get(channelId) || 0;
181 |         if (now - lastMessageTime < this.config.Timing.WaitTime) {
182 |             return false;
183 |         }
184 |
185 |         // 检查同用户阈值
186 |         const lastUserMessageTime = this.lastMessageTimes.get(userChannelKey) || 0;
187 |         if (now - lastUserMessageTime < this.config.Timing.SameUserThreshold) {
188 |             return false;
189 |         }
190 |
191 |         // 更新时间记录
192 |         this.lastMessageTimes.set(channelId, now);
193 |         this.lastMessageTimes.set(userChannelKey, now);
194 |
195 |         return true;
196 |     }
197 |
198 |     /**
199 |      * 评估回复策略
200 |      */
201 |     private async evaluateReplyStrategies(ctx: MiddlewareContext): Promise<boolean> {
202 |         const channelId = ctx.koishiSession.channelId;
203 |         const userId = ctx.koishiSession.userId;
204 |         const strategies = this.config.Strategies;
205 |         let shouldReply = false;
206 |         const reasons: string[] = [];
207 |
208 |         // 策略评估标题
209 |         this.logger.info(`   📊 回复策略评估:`);
210 |
211 |         // 1. @提及策略
212 |         if (strategies.AtMention.Enabled) {
213 |             const probability = strategies.AtMention.Probability;
214 |
215 |             if (ctx.isMentioned) {
216 |                 const willReply = Math.random() < probability;
217 |                 const result = willReply ? "✅ 触发" : "❌ 未触发";
218 |                 shouldReply = willReply;
219 |
220 |                 this.logger.info(`     🔔 @提及策略: ${result} (概率: ${probability}, 随机值: ${Math.random().toFixed(2)})`);
221 |                 reasons.push(`@提及${result.includes("✅") ? "触发" : "未触发"}`);
222 |
223 |                 // 增加意愿值
224 |                 const increase = this.config.Advanced?.Willingness?.AtIncrease || 30;
225 |                 this.willingnessManager.increaseWillingness(channelId, increase);
226 |                 this.logger.info(`       ↳ 意愿值 +${increase}`);
227 |             } else {
228 |                 this.logger.info(`     🔔 @提及策略: 未提及 (概率: ${probability})`);
229 |             }
230 |         }
231 |
232 |         // 2. 阈值策略
233 |         if (!shouldReply && strategies.Threshold.Enabled) {
234 |             const willingness = this.willingnessManager.getWillingness(channelId);
235 |             const threshold = strategies.Threshold.Value * 100; // 转换为百分比
236 |             const willReply = willingness >= threshold;
237 |             const result = willReply ? "✅ 触发" : "❌ 未触发";
238 |
239 |             this.logger.info(`     📈 阈值策略: ${result} (意愿值: ${willingness.toFixed(1)}/${threshold})`);
240 |
241 |             if (willReply) shouldReply = true;
242 |             reasons.push(`阈值策略${result.includes("✅") ? "触发" : "未触发"}`);
243 |         }
244 |
245 |         // 3. 对话流策略
246 |         if (!shouldReply && strategies.ConversationFlow.Enabled) {
247 |             const analysis = await this.analyzeConversationFlow(ctx);
248 |             const confidenceThreshold = strategies.ConversationFlow.ConfidenceThreshold;
249 |             const willReply = analysis.confidence >= confidenceThreshold && analysis.shouldReply;
250 |             const result = willReply ? "✅ 触发" : "❌ 未触发";
251 |
252 |             this.logger.info(`     💬 对话流策略: ${result} (置信度: ${analysis.confidence.toFixed(2)}/${confidenceThreshold})`);
253 |
254 |             if (willReply) shouldReply = true;
255 |             reasons.push(`对话流策略${result.includes("✅") ? "触发" : "未触发"}`);
256 |         }
257 |
258 |         // 4. 关键词策略
259 |         if (!shouldReply) {
260 |             const hasKeyword = this.checkKeywords(ctx);
261 |             const result = hasKeyword ? "✅ 触发" : "❌ 未触发";
262 |
263 |             this.logger.info(`     🔑 关键词策略: ${result}`);
264 |
265 |             if (hasKeyword) shouldReply = true;
266 |             reasons.push(`关键词策略${result.includes("✅") ? "触发" : "未触发"}`);
267 |         }
268 |
269 |         // 增加基础意愿值
270 |         if (!ctx.isMentioned) {
271 |             const increase = this.config.Advanced?.Willingness?.MessageIncrease || 10;
272 |             this.willingnessManager.increaseWillingness(channelId, increase);
273 |             this.logger.info(`     📈 基础意愿值 +${increase}`);
274 |         }
275 |
276 |         // 汇总评估结果
277 |         if (shouldReply) {
278 |             this.logger.info(`   🎯 满足回复条件: ${reasons.join(" | ")}`);
279 |         } else if (reasons.length > 0) {
280 |             this.logger.info(`   🚫 不满足回复条件: ${reasons.join(" | ")}`);
281 |         } else {
282 |             this.logger.info(`   🚫 不满足回复条件: 无策略触发`);
283 |         }
284 |
285 |         return shouldReply;
286 |     }
287 |
288 |     /**
289 |      * 分析对话流
290 |      */
291 |     private async analyzeConversationFlow(ctx: MiddlewareContext): Promise<ReplyDecision> {
292 |         this.logger.info("     🧠 分析对话流...");
293 |         const channelId = ctx.koishiSession.channelId;
294 |
295 |         // 构建 ChatMessage 对象
296 |         const message: ChatMessage = {
297 |             messageId: ctx.koishiSession.messageId,
298 |             content: ctx.koishiSession.content,
299 |             sender: {
300 |                 id: ctx.koishiSession.userId,
301 |                 name: ctx.koishiSession.author?.name || ctx.koishiSession.author?.nick || "未知用户"
302 |             },
303 |             timestamp: new Date(ctx.koishiSession.timestamp),
304 |             channel: {
305 |                 id: ctx.koishiSession.channelId,
306 |                 type: getChannelType(ctx.koishiSession.channelId)
307 |             }
308 |         };
309 |
310 |         // 分析消息并获取分析结果
311 |         await this.flowAnalyzer.analyzeMessage(channelId, message);
312 |
313 |         const analysis = this.flowAnalyzer.shouldReply(channelId, message);
314 |
315 |         // 记录详细分析结果
316 |         this.logger.info(`       ↳ 回复建议: ${analysis.shouldReply}`);
317 |         this.logger.info(`       ↳ 原因: ${analysis.reason}`);
318 |         this.logger.info(`       ↳ 置信度: ${analysis.confidence.toFixed(2)}`);
319 |
320 |         return analysis;
321 |     }
322 |
323 |     /**
324 |      * 检查关键词
325 |      */
326 |     private checkKeywords(ctx: MiddlewareContext): boolean {
327 |         const keywords = this.config.Advanced?.Willingness?.Keywords;
328 |         if (!keywords || !keywords.List.length) {
329 |             this.logger.info("     🔑 关键词策略: 未配置");
330 |             return false;
331 |         }
332 |
333 |         const content = ctx.koishiSession.content.toLowerCase();
334 |         const matchedKeywords = keywords.List.filter(keyword => content.includes(keyword.toLowerCase()));
335 |
336 |         if (matchedKeywords.length > 0) {
337 |             const increase = keywords.Increase || 10;
338 |             this.willingnessManager.increaseWillingness(ctx.koishiSession.channelId, increase * matchedKeywords.length);
339 |             this.logger.info(`     🔑 关键词策略: ✅ 匹配到关键词: ${matchedKeywords.join(", ")}`);
340 |             this.logger.info(`       ↳ 意愿值 +${increase * matchedKeywords.length}`);
341 |             return true;
342 |         }
343 |
344 |         this.logger.info("     🔑 关键词策略: ❌ 无匹配关键词");
345 |         return false;
346 |     }
347 |
348 |     /**
349 |      * 获取意愿值统计
350 |      */
351 |     getWillingnessStats(): Record<string, number> {
352 |         const stats: Record<string, number> = {};
353 |         for (const [channelId, willingness] of this.willingnessManager["willingnessMap"]) {
354 |             stats[channelId] = willingness;
355 |         }
356 |         return stats;
357 |     }
358 |
359 |     /**
360 |      * 健康检查
361 |      */
362 |     async healthCheck(): Promise<boolean> {
363 |         // 检查配置是否有效
364 |         return this.config.Channels.length > 0;
365 |     }
366 | }

</file_content>
