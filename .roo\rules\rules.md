#### **1. 核心理念 (Core Philosophy)**

*   **保持简单 (KISS):** 优先选择简单、直接的解决方案，坚决避免过度抽象、过度工程化和不必要的复杂性。
*   **可读性优先:** 编写自解释的代码。使用清晰、有意义的变量名、函数名和类名，使代码易于人类理解和长期维护。

#### **2. 架构与设计 (Architecture & Design)**

*   **单一职责原则 (SRP):** 使用职责清晰、功能单一的专门化类（Specialized Class）来构建代码。每个类都应有明确的单一目标。
*   **模块化与服务注册:**
    *   遵循模块化架构，将功能封装在独立的服务（Service）中。
    *   所有服务必须统一在 Koishi 的 `ctx` 上注册，以供全局调用。

#### **3. 配置管理 (Configuration Management)**

*   **完全配置驱动:** 严禁在代码中硬编码任何可配置值。所有配置项都必须在 `Config` Schema 中定义。
*   **依赖框架能力:** 禁止自行实现配置的校验或默认值处理。完全依赖 Koishi 框架提供的内置功能。
*   **命名规范:** 配置对象的属性名（Property）统一采用 `PascalCase`。
    *   示例: `Config.MyAwesomeSetting`

#### **4. 代码风格与命名 (Code Style & Naming)**

*   **文件命名:** 所有 TypeScript 文件（`.ts`）名统一使用 `kebab-case` (短横线连接法)。
    *   示例: `my-awesome-service.ts`, `prompt-builder.ts`

#### **5. 测试 (Testing)**

*   **测试框架:** 项目统一使用 `Bun` 进行测试。