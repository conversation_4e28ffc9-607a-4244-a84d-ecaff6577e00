## **Letta Agent: 系统与提示词架构文档**

### **1. 核心设计哲学**

本系统旨在构建一个名为**Letta**的高级AI助手，其核心特性如下：

*   **事件驱动 (Event-Driven):** Letta不仅仅响应用户消息，它能感知并响应一个由多种事件（如成员变动、系统通知等）构成的动态环境。
*   **状态化与多通道意识 (Stateful & Multi-Channel Consciousness):** Letta能够同时感知和参与多个独立的对话频道（群聊/私聊），并在它们之间建立联系，形成一个统一的世界观。
*   **主动式推理 (Proactive Reasoning):** 通过结构化的思考流程，引导Letta从一个被动的响应者转变为一个能够主动发现机会、管理记忆、并根据潜台词行动的智能体。
*   **可观察与可调试 (Observable & Debuggable):** 系统的每一步思考、行动和结果都被结构化地记录下来，为调试、分析和未来的模型迭代提供了坚实的基础。

---

### **2. 核心数据结构: `<world_state>`**

`<world_state>`是每次调用LLM时，系统动态构建的、描述当前所有相关环境信息的XML文档。它是Letta感知世界的唯一入口。

**完整结构概览:**

```xml
<world_state timestamp="[ISO_TIMESTAMP]">
    <active_channels>
        <channel>
            <!-- 频道元数据 -->
            <!-- 成员信息与摘要 -->
            <!-- 对话/事件历史 -->
        </channel>
        <!-- ...更多活动频道 -->
    </active_channels>
    <inactive_channels>
        <!-- ...无新事件的频道ID列表 -->
    </inactive_channels>
</world_state>
```

#### **2.1. `<channel>` 结构**

代表一个独立的对话场景。

*   `<channel id="[string]" type="[group|dm]">`: 频道的唯一标识符和类型。
*   `<name>`: 频道的名称。
*   `<description>`: 频道的描述或主题。
*   `<members>`: **核心及上下文相关**的成员列表。
    *   `<user id="[string]" role="[you|owner|admin]">`: 成员信息。`role="you"`对AI的自我认知至关重要。
*   `<member_summary>`: **大规模群聊优化**，提供宏观统计信息。
    *   `<total_count>`, `<online_count>`, `<recent_active_members_count>`
*   `<history>`: 频道的行动日志，由一系列`<turn>`组成。

#### **2.2. `<turn>` 结构：Agent的核心生命周期单元**

`turn`记录了一次完整的“刺激-反应”循环。

*   `<turn id="[string]" status="[new|in_progress|completed|summarized]">`: 回合的唯一ID和状态。
    *   `new`: 由新事件触发，等待AI处理。
    *   `in_progress`: AI请求了心跳，正在进行链式思考。
    *   `completed`: 回合已结束。
    *   `summarized`: (未来扩展) 旧的回合被摘要以节省Token。
*   `<events>`: 触发或组成此回合的所有外部事件的集合。
*   `<responses>`: AI在此回合中已执行的“心跳”响应列表。

#### **2.3. `<events>` 结构：事件驱动的核心**

*   `<event type="[string]" id="[string]" timestamp="[ISO_TIMESTAMP]">`: 每个事件都有类型、ID和时间戳。
*   **事件具体内容**: 根据`type`不同，内部结构也不同。
    *   **`message`**: `<actor id="...">...</actor><content>...</content>`
    *   **`member_join`**: `<user_joined id="...">...</user_joined>`
    *   **`user_muted`**: `<muter.../><muted_user.../><duration.../>`
    *   此结构可无限扩展以适应新的事件类型。

#### **2.4. `<responses>` 结构：AI行动的永久记录**

*   `<response>`: 代表一次“心跳”（一次LLM调用和执行）。
*   `<thoughts>`: 结构化的思考过程，包含`<observe>`, `<analyze_infer>`, `<plan>`。
*   `<actions>`: 一个或多个`<action>`的集合，记录了AI执行的工具调用。
*   `<observations>`: 一个或多个`<observation>`的集合，记录了工具调用的结果。

---

### **3. 主提示词 (Master Prompt)**

这是注入到系统中的核心指令集。`{{...}}`为Mustache模板占位符。

```prompt
# [行动指令 (Action Directives)]
你是一个名为Letta的AI助手...（完整人设描述）

# [世界观：事件驱动的日志意识 (World View: Event-Driven Log Consciousness)]
Letta, 你通过一个结构化的**行动日志 (Action Log)**来感知世界。每个频道的历史都由一系列的**回合 (`<turn>`)**组成。
...（详细解释turn, events, responses等概念）...
**你的任务:**
你的目标是为所有`status="new"`或`status="in_progress"`的`<turn>`生成一个完整的、包含`thoughts`, `actions`, `request_heartbeat`的JSON响应...

# [思考与行动框架 (Think-Act Framework)]
你的每一次回应都必须严格遵循以下四步思考流程...
1. [OBSERVE] - 环境感知
2. [ANALYZE & INFER] - 深度分析 (包含记忆交叉引用)
3. [PLAN] - 制定行动计划
4. [ACT] - 执行行动 (生成最终JSON)

# [JSON输出规则与心跳机制 (JSON Output Rules & Heartbeat Mechanism)]
1. **最终输出格式:** 你的全部响应必须是一个单一的、顶层JSON对象，包含`thoughts`, `actions` (数组), 和 `request_heartbeat` (布尔值)。
2. **`request_heartbeat` 决策逻辑:** ...（详细解释何时为true/false）...

# [格式与示例 (Format & Examples)]
...（提供至少两个清晰的、包含完整顶层JSON结构的示例）...

# [可用函数 (Available functions)]
{{TOOL_DEFINITION}}
```

---

### **4. Agent的输出：响应JSON结构**

LLM的输出被严格规范为一个单一的JSON对象，便于解析和执行。

```json
{
  "thoughts": {
    "observe": "...",
    "analyze_infer": "...",
    "plan": "..."
  },
  "actions": [
    {
      "function": "function_name_1",
      "params": {
        "inner_thoughts": "...",
        "param1": "value1"
      }
    },
    {
      "function": "function_name_2",
      "params": { ... }
    }
  ],
  "request_heartbeat": true
}
```

---

### **5. 核心逻辑循环：系统实现指南**

这是后端系统驱动Letta运行的核心算法。

1.  **步骤1: 事件聚合 (Event Aggregation)**
    *   从所有来源（IM消息、系统通知、数据库Webhooks等）收集自上次循环以来的所有新`events`。
    *   根据`channel_id`将事件分组。

2.  **步骤2: 世界状态构建 (World State Construction)**
    *   对于每个有新事件的`channel`，创建一个新的`<turn status="new">`并填入聚合的`<events>`。
    *   对于需要**连续心跳**的`channel`，找到其`status="in_progress"`的`<turn>`，将上一步的`response`追加到其`<responses>`列表中，准备下一次调用。
    *   构建完整的`<world_state>` XML字符串，并将其与主提示词结合。

3.  **步骤3: LLM API调用 (LLM API Call)**
    *   将最终的Prompt发送给LLM API。

4.  **步骤4: 响应解析与验证 (Response Parsing & Validation)**
    *   接收LLM的响应。
    *   **严格验证**其是否为符合我们规范的单一JSON对象。
    *   如果解析失败或格式错误，进入**错误处理流程**（例如：重试、使用简化的提示词请求修复、或记录错误并放弃当前回合）。
    *   成功解析后，提取`thoughts`, `actions`, `request_heartbeat`。

5.  **步骤5: 行动执行 (Action Execution)**
    *   将`thoughts`内容持久化存储，用于日志和调试。
    *   按顺序迭代`actions`数组。
    *   对于每个`action`，调用相应的工具函数执行器。
    *   记录每个`action`的执行结果（`observation`），包括成功/失败状态和返回值/错误信息。

6.  **步骤6: 状态更新与循环决策 (State Update & Loop Decision)**
    *   将`thoughts`, `actions`, `observations`组合成一个`<response>`对象，更新到当前`turn`的`<responses>`列表中。
    *   **检查顶层的`request_heartbeat`值:**
        *   **如果为 `true`:** 将当前`turn`的`status`标记为`in_progress`。将此`turn`立即放回处理队列的顶端，返回**步骤2**，开始下一次心跳。
        *   **如果为 `false`:** 将当前`turn`的`status`标记为`completed`。此回合的逻辑链结束。系统可以继续处理队列中的下一个`turn`，或进入空闲状态等待新事件。

---

### **6. 最佳实践与未来演进**

*   **RAG-MCP的应用**: `{{TOOL_DEFINITION}}`部分是应用RAG-MCP的最佳位置。当工具库庞大时，不应将所有工具都注入提示词。而应在`PLAN`阶段，让LLM生成一个对所需能力的描述（如“我需要一个搜索网页的工具”），系统通过RAG检索到最匹配的工具，再将其定义注入到一个临时的、更小的提示词中，让LLM生成最终的`action`。
*   **上下文压缩**: 积极利用`<turn status="summarized">`。可以实现一个独立的Agent或服务，定期将旧的、`completed`的`turn`进行摘要，以控制Prompt的长度。
*   **错误处理**: 为工具执行失败设计重试和回退机制。LLM在下一次心跳中应该能看到失败的`observation`，并据此调整其计划。
*   **模型微调**: 持久化存储的`thoughts`和`actions`是极其宝贵的训练数据。未来可以使用这些数据对模型进行微调，使其行为模式更符合预期，减少对复杂提示词的依赖。